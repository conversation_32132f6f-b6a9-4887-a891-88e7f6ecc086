<!--result.wxml-->
<view class="container">
    <!-- 结果展示 -->
    <view class="result-card">
      <view class="result-icon">
        {{percentage >= 80 ? '🏆' : percentage >= 60 ? '👏' : '💪'}}
      </view>
      <view class="result-title">默契挑战完成！</view>
      <view class="result-score">
        <text class="score-number">{{percentage}}%</text>
        <text class="score-label">默契度</text>
      </view>
      <view class="result-detail">
        答对 {{correctAnswers}} / {{totalQuestions}} 题
      </view>
      <view class="result-comment">
        <text wx:if="{{percentage >= 80}}">🎉 你们真是心有灵犀！</text>
        <text wx:elif="{{percentage >= 60}}">👍 默契度不错，继续加油！</text>
        <text wx:else>💪 多了解彼此，默契会更好哦~</text>
      </view>
    </view>

    <!-- 详细结果 -->
    <view class="detail-results" wx:if="{{questionResults.length > 0}}">
      <view class="subtitle">题目回顾</view>
      <view class="question-review" wx:for="{{questionResults}}" wx:key="index">
        <view class="question-header">
          <text class="question-number">第{{index + 1}}题</text>
          <view class="question-status {{item.isCorrect ? 'correct' : 'wrong'}}">
            {{item.isCorrect ? '✓' : '✗'}}
          </view>
        </view>
        <view class="question-text">{{item.question}}</view>
        <view class="answers-comparison">
          <view class="answer-row" wx:for="{{item.answers}}" wx:for-item="answer" wx:key="playerId">
            <text class="player-name">{{answer.playerName}}</text>
            <text class="player-answer">{{answer.answerText}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-card">
      <view class="subtitle">游戏统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{gameStats.gamesPlayed}}</text>
          <text class="stat-label">总游戏次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{gameStats.averageScore}}%</text>
          <text class="stat-label">平均默契度</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="pixel-btn secondary" bindtap="backToHome">
        🏠 返回首页
      </button>
      <button class="pixel-btn" bindtap="playAgain">
        🔄 再玩一局
      </button>
    </view>

  <!-- 分享按钮 -->
  <view class="share-section">
    <button class="pixel-btn accent" open-type="share">
      📤 分享结果
    </button>
  </view>
</view>
