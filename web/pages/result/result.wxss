/**result.wxss**/
page {
  background: var(--bg-primary);
}

.container {
  min-height: 100vh;
  padding: 40rpx;
  box-sizing: border-box;
}

.result-card {
  background: var(--bg-secondary);
  border: 6rpx solid var(--success-color);
  border-radius: var(--border-radius);
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
  box-shadow: var(--shadow);
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 40rpx;
}

.result-score {
  margin-bottom: 20rpx;
}

.score-number {
  display: block;
  font-size: 80rpx;
  font-weight: bold;
  color: var(--primary-color);
  line-height: 1;
}

.score-label {
  display: block;
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-top: 10rpx;
}

.result-detail {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 30rpx;
}

.result-comment {
  font-size: 32rpx;
  color: var(--text-primary);
  line-height: 1.4;
}

.detail-results {
  margin-bottom: 40rpx;
}

.question-review {
  background: var(--bg-secondary);
  border: 4rpx solid var(--bg-dark);
  border-radius: var(--border-radius);
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.question-number {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.question-status {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.question-status.correct {
  background: var(--success-color);
}

.question-status.wrong {
  background: var(--warning-color);
}

.question-text {
  font-size: 30rpx;
  color: var(--text-primary);
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.answers-comparison {
  background: var(--bg-primary);
  border-radius: 10rpx;
  padding: 20rpx;
}

.answer-row {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #E0E0E0;
}

.answer-row:last-child {
  border-bottom: none;
}

.player-name {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.player-answer {
  font-size: 26rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.stats-card {
  background: var(--bg-secondary);
  border: 4rpx solid var(--accent-color);
  border-radius: var(--border-radius);
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-buttons .pixel-btn {
  flex: 1;
}

.share-section {
  text-align: center;
}
