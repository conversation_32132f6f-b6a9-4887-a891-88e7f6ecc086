// result.js
Page({
  data: {
    percentage: 0,
    correctAnswers: 0,
    totalQuestions: 5,
    questionResults: [],
    gameStats: {
      gamesPlayed: 0,
      averageScore: 0
    }
  },

  onLoad(options) {
    // 从参数或本地存储获取结果数据
    const percentage = parseInt(options.percentage) || 0
    const correctAnswers = parseInt(options.correctAnswers) || 0
    
    this.setData({
      percentage: percentage,
      correctAnswers: correctAnswers
    })

    this.loadGameStats()
    this.loadQuestionResults()
  },

  // 加载游戏统计
  loadGameStats() {
    const gameStats = wx.getStorageSync('gameStats') || {
      gamesPlayed: 0,
      totalScore: 0,
      averageScore: 0
    }

    this.setData({
      gameStats: gameStats
    })
  },

  // 加载题目结果详情
  loadQuestionResults() {
    const questionResults = wx.getStorageSync('lastGameResults') || []
    this.setData({
      questionResults: questionResults
    })
  },

  // 返回首页
  backToHome() {
    wx.reLaunch({
      url: '/pages/index/index'
    })
  },

  // 再玩一局
  playAgain() {
    const nickname = wx.getStorageSync('nickname') || ''
    wx.redirectTo({
      url: `/pages/library/library?nickname=${nickname}`
    })
  },

  // 分享功能
  onShareAppMessage() {
    const percentage = this.data.percentage
    let shareTitle = ''
    
    if (percentage >= 80) {
      shareTitle = `我们的默契度达到了${percentage}%！真是心有灵犀 🎉`
    } else if (percentage >= 60) {
      shareTitle = `我们的默契度是${percentage}%，还不错哦 👏`
    } else {
      shareTitle = `我们的默契度是${percentage}%，需要更多了解呢 💪`
    }

    return {
      title: shareTitle,
      path: '/pages/index/index',
      imageUrl: '' // 可以设置分享图片
    }
  }
})
