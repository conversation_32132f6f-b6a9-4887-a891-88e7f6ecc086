// library.js
Page({
  data: {
    nickname: '',
    libraries: [],
    loading: false,
    loadingText: ''
  },

  onLoad(options) {
    this.setData({
      nickname: options.nickname || ''
    })
    this.loadLibraries()
  },

  // 加载题库列表
  loadLibraries() {
    this.setData({
      loading: true,
      loadingText: '加载题库中...'
    })

    const app = getApp()
    wx.request({
      url: app.globalData.serverUrl + '/api/libraries',
      method: 'GET',
      success: (res) => {
        this.setData({
          loading: false
        })

        if (res.data.success) {
          const libraries = res.data.data.map(lib => {
            // 检查解锁状态
            const unlocked = this.checkLibraryUnlock(lib.library_id)
            return {
              ...lib,
              unlocked: lib.unlocked === 1 || unlocked
            }
          })

          this.setData({
            libraries: libraries
          })
        } else {
          wx.showToast({
            title: '加载题库失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        this.setData({
          loading: false
        })
        console.error('加载题库失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 检查题库解锁状态
  checkLibraryUnlock(libraryId) {
    // 从本地存储获取游戏统计
    const gameStats = wx.getStorageSync('gameStats') || {
      gamesPlayed: 0,
      totalScore: 0,
      averageScore: 0
    }

    switch (libraryId) {
      case 'food':
        return gameStats.gamesPlayed >= 3
      case 'movie':
        return gameStats.averageScore >= 80
      default:
        return true
    }
  },

  // 选择题库
  selectLibrary(e) {
    const library = e.currentTarget.dataset.library

    if (!library.unlocked) {
      let unlockCondition = ''
      switch (library.library_id) {
        case 'food':
          unlockCondition = '需要完成3局游戏才能解锁'
          break
        case 'movie':
          unlockCondition = '需要平均默契度达到80%才能解锁'
          break
      }

      wx.showModal({
        title: '题库未解锁',
        content: unlockCondition,
        showCancel: false,
        confirmText: '知道了'
      })
      return
    }

    // 创建房间
    this.createRoom(library.library_id)
  },

  // 创建房间
  createRoom(libraryId) {
    this.setData({
      loading: true,
      loadingText: '创建房间中...'
    })

    const app = getApp()
    wx.request({
      url: app.globalData.serverUrl + '/api/room/create',
      method: 'POST',
      data: {
        libraryId: libraryId,
        playerNickname: this.data.nickname
      },
      success: (res) => {
        this.setData({
          loading: false
        })

        if (res.data.success) {
          const roomId = res.data.data.roomId
          
          // 跳转到游戏页面
          wx.redirectTo({
            url: `/pages/game/game?roomId=${roomId}&nickname=${this.data.nickname}&libraryId=${libraryId}&isHost=true`
          })
        } else {
          wx.showToast({
            title: res.data.message || '创建房间失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        this.setData({
          loading: false
        })
        console.error('创建房间失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  }
})
