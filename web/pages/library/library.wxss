/**library.wxss**/
page {
  background: var(--bg-primary);
}

.container {
  min-height: 100vh;
  padding: 40rpx;
  box-sizing: border-box;
}

.header {
  text-align: center;
  padding: 60rpx 0 40rpx 0;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: var(--secondary-color);
  margin-bottom: 20rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
}

.library-list {
  margin-bottom: 40rpx;
}

.library-item {
  display: flex;
  align-items: center;
  background: var(--bg-secondary);
  border: 6rpx solid var(--bg-dark);
  border-radius: var(--border-radius);
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
}

.library-item:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
}

.library-item.locked {
  background: #F8F9FA;
  border-color: #BDC3C7;
  opacity: 0.7;
}

.library-item.locked:active {
  transform: none;
}

.library-icon {
  font-size: 64rpx;
  margin-right: 30rpx;
  line-height: 1;
}

.library-info {
  flex: 1;
}

.library-name {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 10rpx;
}

.library-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 10rpx;
}

.library-status {
  display: flex;
  align-items: center;
}

.lock-icon {
  font-size: 24rpx;
  margin-right: 10rpx;
}

.unlock-text {
  font-size: 24rpx;
  color: var(--warning-color);
}

.library-arrow {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: bold;
}

.unlock-tips {
  background: var(--bg-secondary);
  border: 4rpx solid var(--accent-color);
  border-radius: var(--border-radius);
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 20rpx;
}

.tips-item {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 40rpx;
  border-radius: var(--border-radius);
  z-index: 1000;
}
