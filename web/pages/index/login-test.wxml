<!-- 登录功能测试页面 -->
<view class="test-container">
  <view class="test-header">
    <text class="test-title">登录功能测试</text>
  </view>

  <!-- 当前用户状态 -->
  <view class="status-section">
    <view class="status-title">当前状态</view>
    <view class="status-item">
      <text class="label">登录状态:</text>
      <text class="value {{userInfo.nickName ? 'success' : 'error'}}">
        {{userInfo.nickName ? '已登录' : '未登录'}}
      </text>
    </view>
    <view class="status-item" wx:if="{{userInfo.nickName}}">
      <text class="label">用户昵称:</text>
      <text class="value">{{userInfo.nickName}}</text>
    </view>
    <view class="status-item" wx:if="{{openid}}">
      <text class="label">OpenID:</text>
      <text class="value small">{{openid}}</text>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="test-actions">
    <button class="test-btn primary" bindtap="testLogin" disabled="{{loginLoading}}">
      {{loginLoading ? '登录中...' : '测试登录'}}
    </button>
    
    <button class="test-btn secondary" bindtap="testGetOpenId" disabled="{{loginLoading}}">
      测试获取OpenID
    </button>
    
    <button class="test-btn warning" bindtap="testSaveUser" disabled="{{!userInfo.nickName}}">
      测试保存用户信息
    </button>
    
    <button class="test-btn danger" bindtap="testLogout" disabled="{{!userInfo.nickName}}">
      测试退出登录
    </button>
  </view>

  <!-- 日志显示 -->
  <view class="log-section">
    <view class="log-title">操作日志</view>
    <scroll-view class="log-content" scroll-y="true">
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message {{item.type}}">{{item.message}}</text>
      </view>
    </scroll-view>
    <button class="clear-btn" bindtap="clearLogs">清空日志</button>
  </view>
</view>
