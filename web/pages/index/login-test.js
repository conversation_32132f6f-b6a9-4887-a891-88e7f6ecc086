// 登录功能测试页面
Page({
  data: {
    userInfo: {},
    openid: '',
    loginLoading: false,
    logs: []
  },

  onLoad() {
    this.loadUserInfo()
    this.addLog('页面加载完成', 'info')
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo') || {}
    const openid = wx.getStorageSync('openid') || ''
    
    this.setData({
      userInfo,
      openid
    })
    
    if (userInfo.nickName) {
      this.addLog(`用户信息加载成功: ${userInfo.nickName}`, 'success')
    } else {
      this.addLog('未找到用户信息', 'warning')
    }
  },

  // 测试登录流程
  async testLogin() {
    this.addLog('开始测试登录流程...', 'info')
    this.setData({ loginLoading: true })

    try {
      // 1. 测试获取OpenID
      const openid = await this.getOpenId()
      this.addLog(`获取OpenID成功: ${openid}`, 'success')

      // 2. 模拟用户信息
      const userInfo = {
        openid: openid,
        nickName: `测试用户_${Date.now().toString().slice(-4)}`,
        avatarUrl: '',
        level: 1,
        score: 0,
        createTime: new Date().toISOString()
      }

      // 3. 保存到本地
      wx.setStorageSync('userInfo', userInfo)
      wx.setStorageSync('openid', openid)

      // 4. 保存到服务器
      await this.saveUserToDatabase(userInfo)

      // 5. 更新页面状态
      this.setData({
        userInfo,
        openid,
        loginLoading: false
      })

      this.addLog('登录流程测试成功！', 'success')

    } catch (error) {
      this.addLog(`登录流程测试失败: ${error.message}`, 'error')
      this.setData({ loginLoading: false })
    }
  },

  // 测试获取OpenID
  async testGetOpenId() {
    this.addLog('开始测试获取OpenID...', 'info')
    
    try {
      const openid = await this.getOpenId()
      this.setData({ openid })
      this.addLog(`OpenID获取成功: ${openid}`, 'success')
    } catch (error) {
      this.addLog(`OpenID获取失败: ${error.message}`, 'error')
    }
  },

  // 测试保存用户信息
  async testSaveUser() {
    if (!this.data.userInfo.nickName) {
      this.addLog('请先登录', 'warning')
      return
    }

    this.addLog('开始测试保存用户信息...', 'info')
    
    try {
      await this.saveUserToDatabase(this.data.userInfo)
      this.addLog('用户信息保存成功', 'success')
    } catch (error) {
      this.addLog(`用户信息保存失败: ${error.message}`, 'error')
    }
  },

  // 测试退出登录
  testLogout() {
    this.addLog('开始测试退出登录...', 'info')
    
    // 清除本地存储
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('openid')
    
    this.setData({
      userInfo: {},
      openid: ''
    })
    
    this.addLog('退出登录成功', 'success')
  },

  // 获取OpenID
  getOpenId() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            this.addLog(`wx.login成功，code: ${res.code}`, 'info')
            
            wx.request({
              url: `${getApp().globalData.serverUrl}/api/auth/login`,
              method: 'POST',
              data: { code: res.code },
              success: (response) => {
                if (response.data.success && response.data.data.openid) {
                  resolve(response.data.data.openid)
                } else {
                  reject(new Error(response.data.message || '获取openid失败'))
                }
              },
              fail: (error) => {
                this.addLog(`请求失败: ${JSON.stringify(error)}`, 'error')
                reject(error)
              }
            })
          } else {
            reject(new Error(res.errMsg))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  },

  // 保存用户信息到数据库
  saveUserToDatabase(userInfo) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${getApp().globalData.serverUrl}/api/user/save`,
        method: 'POST',
        data: userInfo,
        success: (res) => {
          if (res.data.success) {
            resolve(res.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },

  // 添加日志
  addLog(message, type = 'info') {
    const time = new Date().toLocaleTimeString()
    const logs = this.data.logs
    logs.unshift({ time, message, type })
    
    // 限制日志数量
    if (logs.length > 50) {
      logs.splice(50)
    }
    
    this.setData({ logs })
    console.log(`[${type.toUpperCase()}] ${message}`)
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] })
    this.addLog('日志已清空', 'info')
  }
})
