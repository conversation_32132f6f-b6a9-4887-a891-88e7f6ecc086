Page({
  data: {
    showAuth: false,
    tempAvatarUrl: '',
    tempNickname: 'VolCano'
  },

  showAuth() {
    this.setData({
      showAuth: true
    })
  },

  hideAuth() {
    this.setData({
      showAuth: false
    })
  },

  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    this.setData({
      tempAvatarUrl: avatarUrl
    })
    console.log('选择头像:', avatarUrl)
  },

  onNicknameInput(e) {
    this.setData({
      tempNickname: e.detail.value
    })
  },

  confirmAuth() {
    if (!this.data.tempNickname) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    wx.showToast({
      title: '授权成功',
      icon: 'success'
    })

    this.setData({
      showAuth: false
    })
  }
})
