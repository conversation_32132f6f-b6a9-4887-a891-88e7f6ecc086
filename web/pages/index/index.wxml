<!--index.wxml-->
<view class="container">

  <!-- 顶部用户信息栏 -->
  <view class="top-bar">
    <view class="game-info">
      <view class="game-title">默契挑战</view>
      <view class="game-subtitle">测试你们的默契程度</view>
    </view>
    <view class="user-section">
      <view class="user-info" wx:if="{{userInfo.nickName}}">
        <view class="avatar-placeholder" wx:if="{{!userInfo.avatarUrl}}">👤</view>
        <image wx:else class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-details">
          <view class="user-name">{{userInfo.nickName}}</view>
          <view class="user-level">Lv.{{userInfo.level || 1}}</view>
        </view>
      </view>
      <view class="login-prompt" wx:else bindtap="showLoginModal">
        <view class="avatar-placeholder">👤</view>
        <view class="login-text">点击登录</view>
      </view>
    </view>
  </view>

  <!-- 主要功能区域 -->
  <view class="main-functions">

    <!-- 创建房间 -->
    <view class="function-card primary" bindtap="handleCreateRoom">
      <view class="card-icon">🎮</view>
      <view class="card-content">
        <view class="card-title">创建房间</view>
        <view class="card-desc">邀请好友一起玩</view>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 快速加入房间区域 -->
    <view class="quick-join-card">
      <view class="card-header">
        <view class="card-icon">⚡</view>
        <view class="card-content">
          <view class="card-title">快速加入</view>
          <view class="card-desc">输入房间号立即开始</view>
        </view>
      </view>
      <view class="join-input-group">
        <input
          class="room-input"
          placeholder="输入4位房间号"
          value="{{roomId}}"
          bindinput="onRoomIdInput"
          type="number"
          maxlength="4"
        />
        <button
          class="join-btn {{!roomId || roomId.length !== 4 ? 'disabled' : ''}}"
          bindtap="handleQuickJoin"
          disabled="{{!roomId || roomId.length !== 4}}"
        >
          加入
        </button>
      </view>
    </view>

    <!-- 加入房间 -->
    <view class="function-card secondary" bindtap="handleJoinRoom">
      <view class="card-icon">🚪</view>
      <view class="card-content">
        <view class="card-title">加入房间</view>
        <view class="card-desc">通过弹窗输入房间号</view>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 题库管理 -->
    <view class="function-card accent" bindtap="handleLibrary">
      <view class="card-icon">📚</view>
      <view class="card-content">
        <view class="card-title">题库管理</view>
        <view class="card-desc">查看和管理题目</view>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 游戏历史 -->
    <view class="function-card warning" bindtap="handleHistory">
      <view class="card-icon">📊</view>
      <view class="card-content">
        <view class="card-title">游戏历史</view>
        <view class="card-desc">查看历史记录和统计</view>
      </view>
      <view class="card-arrow">→</view>
    </view>

  </view>

  <!-- 登录弹窗 -->
  <view class="login-modal" wx:if="{{showLoginModal}}">
    <view class="modal-mask" bindtap="hideLoginModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">登录游戏</view>
        <view class="modal-close" bindtap="hideLoginModal">×</view>
      </view>

      <view class="login-form">
        <view class="login-tip">
          <text>获取微信头像和昵称开始游戏</text>
        </view>

        <view class="user-preview">
          <view class="avatar-placeholder" wx:if="{{!tempAvatarUrl}}">👤</view>
          <image wx:else class="avatar" src="{{tempAvatarUrl}}" mode="aspectFill"></image>
          <view class="preview-name">{{tempNickname || '未设置昵称'}}</view>
        </view>

        <button
          class="btn primary"
          open-type="chooseAvatar"
          bind:chooseavatar="onChooseAvatar"
        >
          获取头像
        </button>

        <input
          class="input"
          type="nickname"
          placeholder="请输入昵称"
          value="{{tempNickname}}"
          bindinput="onNicknameInput"
          maxlength="10"
        />

        <button
          class="btn accent {{!tempNickname ? 'disabled' : ''}}"
          bindtap="confirmUserInfo"
          disabled="{{!tempNickname}}"
        >
          确认登录
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>{{loadingText}}</text>
  </view>
</view>
