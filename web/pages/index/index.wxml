<!--index.wxml-->
<view class="container">

  <!-- 顶部用户信息栏 -->
  <view class="top-bar">
    <view class="game-info">
      <view class="game-title">默契挑战</view>
      <view class="game-subtitle">测试你们的默契程度</view>
    </view>
    <view class="user-section">
      <view class="user-info" wx:if="{{userInfo.nickName}}">
        <view class="avatar-placeholder" wx:if="{{!userInfo.avatarUrl}}">👤</view>
        <image wx:else class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-details">
          <view class="user-name">{{userInfo.nickName}}</view>
          <view class="user-level">Lv.{{userInfo.level || 1}}</view>
        </view>
      </view>
      <view class="login-prompt" wx:else bindtap="showLoginModal">
        <view class="avatar-placeholder">👤</view>
        <view class="login-text">点击登录</view>
      </view>
    </view>
  </view>

  <!-- 主要功能区域 -->
  <view class="main-functions">

    <!-- 创建房间 -->
    <view class="function-card primary" bindtap="handleCreateRoom">
      <view class="card-icon">🎮</view>
      <view class="card-content">
        <view class="card-title">创建房间</view>
        <view class="card-desc">邀请好友一起玩</view>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 快速加入房间区域 -->
    <view class="quick-join-card">
      <view class="card-header">
        <view class="card-icon">⚡</view>
        <view class="card-content">
          <view class="card-title">快速加入</view>
          <view class="card-desc">输入房间号立即开始</view>
        </view>
      </view>
      <view class="join-input-group">
        <input
          class="room-input"
          placeholder="输入4位房间号"
          value="{{roomId}}"
          bindinput="onRoomIdInput"
          type="number"
          maxlength="4"
        />
        <button
          class="join-btn {{!roomId || roomId.length !== 4 ? 'disabled' : ''}}"
          bindtap="handleQuickJoin"
          disabled="{{!roomId || roomId.length !== 4}}"
        >
          加入
        </button>
      </view>
    </view>

    <!-- 加入房间 -->
    <view class="function-card secondary" bindtap="handleJoinRoom">
      <view class="card-icon">🚪</view>
      <view class="card-content">
        <view class="card-title">加入房间</view>
        <view class="card-desc">通过弹窗输入房间号</view>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 题库管理 -->
    <view class="function-card accent" bindtap="handleLibrary">
      <view class="card-icon">📚</view>
      <view class="card-content">
        <view class="card-title">题库管理</view>
        <view class="card-desc">查看和管理题目</view>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 游戏历史 -->
    <view class="function-card warning" bindtap="handleHistory">
      <view class="card-icon">📊</view>
      <view class="card-content">
        <view class="card-title">游戏历史</view>
        <view class="card-desc">查看历史记录和统计</view>
      </view>
      <view class="card-arrow">→</view>
    </view>

  </view>

  <!-- 微信授权登录弹窗 -->
  <view class="auth-modal" wx:if="{{showLoginModal}}">
    <view class="auth-mask" bindtap="hideLoginModal"></view>
    <view class="auth-content">
      <!-- 应用信息头部 -->
      <view class="auth-header">
        <view class="app-icon">🎮</view>
        <view class="app-name">默契测试</view>
        <view class="auth-type">申请</view>
      </view>

      <!-- 授权说明 -->
      <view class="auth-description">
        <view class="auth-title">获取/设置你的头像、昵称</view>
        <view class="auth-subtitle">让好友识别出你是出题者</view>
      </view>

      <!-- 用户信息预览区域 -->
      <view class="user-info-section">
        <!-- 头像选择 -->
        <view class="info-item">
          <view class="info-label">头像</view>
          <button
            class="avatar-selector"
            open-type="chooseAvatar"
            bind:chooseavatar="onChooseAvatar"
          >
            <view class="current-avatar" wx:if="{{!tempAvatarUrl}}">
              <view class="default-avatar">👤</view>
            </view>
            <image wx:else class="current-avatar" src="{{tempAvatarUrl}}" mode="aspectFill"></image>
            <view class="avatar-arrow">></view>
          </button>
        </view>

        <!-- 昵称输入 -->
        <view class="info-item">
          <view class="info-label">昵称</view>
          <view class="nickname-input-wrapper">
            <input
              class="nickname-input"
              type="nickname"
              placeholder="微信用户"
              value="{{tempNickname}}"
              bindinput="onNicknameInput"
              maxlength="10"
            />
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="auth-actions">
        <button class="auth-btn cancel" bindtap="hideLoginModal">拒绝</button>
        <button
          class="auth-btn confirm {{!tempNickname ? 'disabled' : ''}}"
          bindtap="confirmUserInfo"
          disabled="{{!tempNickname}}"
        >
          保存
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>{{loadingText}}</text>
  </view>
</view>
