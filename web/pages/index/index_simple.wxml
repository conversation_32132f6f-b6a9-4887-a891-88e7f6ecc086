<!--index_simple.wxml - 简化测试版本-->
<view class="container">
  <view class="header">
    <text class="title">🎯 默契挑战</text>
    <text class="subtitle">测试你们的默契程度</text>
  </view>

  <view class="form-section">
    <view class="input-group">
      <text class="label">输入昵称:</text>
      <input 
        class="input" 
        placeholder="请输入昵称" 
        value="{{nickname}}"
        bindinput="onNicknameInput"
      />
    </view>

    <button class="btn primary" bindtap="testAPI">
      🧪 测试API连接
    </button>

    <button class="btn secondary" bindtap="testGame">
      🎮 测试游戏界面
    </button>
  </view>

  <view class="loading" wx:if="{{loading}}">
    <text>{{loadingText}}</text>
  </view>
</view>
