<!-- 测试微信授权弹窗样式 -->
<view class="test-container">
  <button bindtap="showAuth">显示授权弹窗</button>
  
  <!-- 微信授权登录弹窗 -->
  <view class="auth-modal" wx:if="{{showAuth}}">
    <view class="auth-mask" bindtap="hideAuth"></view>
    <view class="auth-content">
      <!-- 应用信息头部 -->
      <view class="auth-header">
        <view class="app-icon">🎮</view>
        <view class="app-name">默契测试</view>
        <view class="auth-type">申请</view>
      </view>

      <!-- 授权说明 -->
      <view class="auth-description">
        <view class="auth-title">获取/设置你的头像、昵称</view>
        <view class="auth-subtitle">让好友识别出你是出题者</view>
      </view>

      <!-- 用户信息预览区域 -->
      <view class="user-info-section">
        <!-- 头像选择 -->
        <view class="info-item">
          <view class="info-label">头像</view>
          <button 
            class="avatar-selector" 
            open-type="chooseAvatar" 
            bind:chooseavatar="onChooseAvatar"
          >
            <view class="current-avatar" wx:if="{{!tempAvatarUrl}}">
              <view class="default-avatar">👤</view>
            </view>
            <image wx:else class="current-avatar" src="{{tempAvatarUrl}}" mode="aspectFill"></image>
            <view class="avatar-arrow">></view>
          </button>
        </view>

        <!-- 昵称输入 -->
        <view class="info-item">
          <view class="info-label">昵称</view>
          <view class="nickname-input-wrapper">
            <input
              class="nickname-input"
              type="nickname"
              placeholder="VolCano"
              value="{{tempNickname}}"
              bindinput="onNicknameInput"
              maxlength="10"
            />
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="auth-actions">
        <button class="auth-btn cancel" bindtap="hideAuth">拒绝</button>
        <button 
          class="auth-btn confirm {{!tempNickname ? 'disabled' : ''}}" 
          bindtap="confirmAuth"
          disabled="{{!tempNickname}}"
        >
          保存
        </button>
      </view>
    </view>
  </view>
</view>
