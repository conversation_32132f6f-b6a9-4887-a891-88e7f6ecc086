// index_basic.js - 最基础版本
Page({
  data: {
    nickname: '',
    loading: false,
    loadingText: ''
  },

  onLoad: function() {
    console.log('页面加载完成')
  },

  onNicknameInput: function(e) {
    this.setData({
      nickname: e.detail.value
    })
    console.log('昵称输入:', e.detail.value)
  },

  testAPI: function() {
    console.log('开始测试API')
    
    this.setData({
      loading: true,
      loadingText: '测试API连接中...'
    })

    var that = this
    wx.request({
      url: 'http://localhost:3000/api/libraries',
      method: 'GET',
      success: function(res) {
        console.log('API测试成功:', res)
        that.setData({
          loading: false
        })
        
        wx.showModal({
          title: 'API测试结果',
          content: '连接成功！状态码: ' + res.statusCode,
          showCancel: false
        })
      },
      fail: function(err) {
        console.log('API测试失败:', err)
        that.setData({
          loading: false
        })
        
        wx.showModal({
          title: 'API测试失败',
          content: '错误: ' + (err.errMsg || '网络连接失败'),
          showCancel: false
        })
      }
    })
  },

  showInfo: function() {
    var info = '昵称: ' + this.data.nickname + '\n'
    info += '当前时间: ' + new Date().toLocaleString()
    
    wx.showModal({
      title: '当前信息',
      content: info,
      showCancel: false
    })
  }
})
