.test-container {
  padding: 100rpx;
  text-align: center;
}

/* 微信授权弹窗样式 */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.auth-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.auth-content {
  position: relative;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-width: 750rpx;
  padding-bottom: env(safe-area-inset-bottom);
  animation: slideUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 10001;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 应用信息头部 */
.auth-header {
  display: flex;
  align-items: center;
  padding: 40rpx 32rpx 32rpx;
  border-bottom: 1rpx solid #e5e5ea;
  position: relative;
}

.app-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #007aff, #5856d6);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}

.app-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-right: 16rpx;
}

.auth-type {
  font-size: 34rpx;
  color: #1d1d1f;
  font-weight: 400;
}

/* 授权说明 */
.auth-description {
  padding: 32rpx;
  text-align: center;
}

.auth-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.auth-subtitle {
  font-size: 28rpx;
  color: #8e8e93;
  line-height: 1.4;
}

/* 用户信息区域 */
.user-info-section {
  padding: 0 32rpx;
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #e5e5ea;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 32rpx;
  color: #1d1d1f;
  font-weight: 500;
}

/* 头像选择器 */
.avatar-selector {
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
  line-height: inherit;
  position: relative;
}

.avatar-selector::after {
  border: none;
}

.current-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f2f2f7;
  border: 2rpx solid #e5e5ea;
}

.current-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.default-avatar {
  font-size: 36rpx;
  color: #8e8e93;
}

.avatar-arrow {
  font-size: 28rpx;
  color: #c7c7cc;
  font-weight: 400;
  transform: rotate(0deg);
}

/* 昵称输入 */
.nickname-input-wrapper {
  display: flex;
  align-items: center;
  min-width: 300rpx;
  justify-content: flex-end;
}

.nickname-input {
  text-align: right;
  font-size: 32rpx;
  color: #1d1d1f;
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
}

.nickname-input::placeholder {
  color: #8e8e93;
}

/* 底部按钮 */
.auth-actions {
  display: flex;
  padding: 32rpx;
  gap: 24rpx;
  background: #f2f2f7;
  border-radius: 0 0 24rpx 24rpx;
}

.auth-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.auth-btn::after {
  border: none;
}

.auth-btn.cancel {
  background: #ffffff;
  color: #007aff;
  border: 1rpx solid #e5e5ea;
}

.auth-btn.cancel:active {
  background: #f2f2f7;
}

.auth-btn.confirm {
  background: #34c759;
  color: #ffffff;
}

.auth-btn.confirm:active {
  background: #30b350;
}

.auth-btn.confirm.disabled {
  background: #c7c7cc;
  color: #ffffff;
  opacity: 0.6;
}
