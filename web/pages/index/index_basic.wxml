<!--index_basic.wxml - 最基础版本-->
<view style="padding: 40rpx;">
  <view style="text-align: center; margin-bottom: 60rpx;">
    <text style="font-size: 48rpx; color: #FF6B6B; font-weight: bold;">默契挑战</text>
  </view>
  
  <view style="margin-bottom: 40rpx;">
    <text style="font-size: 32rpx; color: #333;">输入昵称:</text>
    <input 
      style="width: 100%; padding: 24rpx; border: 2rpx solid #ddd; border-radius: 12rpx; font-size: 32rpx; margin-top: 20rpx; box-sizing: border-box;"
      placeholder="请输入昵称" 
      value="{{nickname}}"
      bindinput="onNicknameInput"
    />
  </view>
  
  <button 
    style="width: 100%; padding: 32rpx; background-color: #FF6B6B; color: white; border: none; border-radius: 12rpx; font-size: 32rpx; margin-bottom: 20rpx;"
    bindtap="testAPI"
  >
    测试API连接
  </button>
  
  <button 
    style="width: 100%; padding: 32rpx; background-color: #4ECDC4; color: white; border: none; border-radius: 12rpx; font-size: 32rpx;"
    bindtap="showInfo"
  >
    显示信息
  </button>
  
  <view wx:if="{{loading}}" style="text-align: center; margin-top: 40rpx; color: #666;">
    <text>{{loadingText}}</text>
  </view>
</view>
