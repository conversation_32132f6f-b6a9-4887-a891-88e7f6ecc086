/**index.wxss - 简约白色主界面**/

/* 页面容器 */
.container {
  padding: 0;
  min-height: 100vh;
  background: var(--bg-light);
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 123, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(111, 66, 193, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(23, 162, 184, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 顶部信息栏 */
.top-bar {
  background: var(--bg-white);
  border-bottom: 2rpx solid var(--border-color);
  padding: 40rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.top-bar::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 32rpx;
  right: 32rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #007bff, #17a2b8, #6f42c1, #e83e8c);
  border-radius: 2rpx;
}

.game-info {
  flex: 1;
}

.game-title {
  font-size: 40rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #007bff, #6f42c1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8rpx;
}

.game-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.user-section {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info .avatar,
.user-info .avatar-placeholder {
  width: 80rpx;
  height: 80rpx;
  margin-right: 16rpx;
}

.user-details {
  text-align: right;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.user-level {
  font-size: 22rpx;
  color: #007bff;
  font-weight: 600;
}

.login-prompt {
  display: flex;
  align-items: center;
  padding: 20rpx 28rpx;
  background: linear-gradient(135deg, var(--bg-light), var(--bg-white));
  border-radius: var(--radius-large);
  border: 2rpx solid var(--border-color);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.login-prompt:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.login-prompt .avatar-placeholder {
  width: 60rpx;
  height: 60rpx;
  margin-right: 12rpx;
  font-size: 24rpx;
  background: linear-gradient(135deg, #007bff, #6f42c1);
  color: var(--text-white);
}

.login-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 主要功能区域 */
.main-functions {
  padding: 32rpx;
}

.function-card {
  background: var(--bg-white);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-large);
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.function-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: var(--primary);
  transition: width 0.3s ease;
}

.function-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12);
}

.function-card:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.function-card.primary::before {
  background: linear-gradient(180deg, #007bff, #0056b3);
}

.function-card.secondary::before {
  background: linear-gradient(180deg, #17a2b8, #138496);
}

.function-card.accent::before {
  background: linear-gradient(180deg, #6f42c1, #5a32a3);
}

.function-card.warning::before {
  background: linear-gradient(180deg, #e83e8c, #d91a72);
}

.card-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.card-arrow {
  font-size: 32rpx;
  color: var(--text-muted);
  transition: transform 0.3s ease;
}

.function-card:hover .card-arrow {
  transform: translateX(8rpx);
}

/* 快速加入卡片 */
.quick-join-card {
  background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
  border-radius: var(--radius-large);
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 12rpx 32rpx rgba(0, 123, 255, 0.25);
  color: var(--text-white);
  position: relative;
  overflow: hidden;
}

.quick-join-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.quick-join-card .card-icon {
  color: var(--text-white);
  margin-right: 24rpx;
}

.quick-join-card .card-title {
  color: var(--text-white);
  font-weight: 700;
}

.quick-join-card .card-desc {
  color: rgba(255, 255, 255, 0.9);
}

.join-input-group {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.quick-join-card .room-input {
  flex: 1;
  padding: 28rpx 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius);
  font-size: 36rpx;
  color: var(--text-white);
  text-align: center;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.quick-join-card .room-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.quick-join-card .room-input:focus {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.3);
}

.quick-join-card .join-btn {
  padding: 28rpx 40rpx;
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-white);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius);
  font-size: 32rpx;
  font-weight: 700;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.quick-join-card .join-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2rpx);
}

.join-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 登录弹窗 */
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
}

.modal-content {
  position: relative;
  background: #ffffff !important;
  border: 2rpx solid #e9ecef;
  border-radius: var(--radius-large);
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  box-shadow: 0 24rpx 80rpx rgba(0, 0, 0, 0.15);
  transform: scale(1);
  animation: modalShow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10001;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 0;
  border-bottom: 2rpx solid var(--border-color);
  margin-bottom: 32rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #007bff, #6f42c1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close {
  font-size: 48rpx;
  color: var(--text-secondary);
  padding: 8rpx;
  line-height: 1;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: var(--bg-light);
  transform: scale(0.9);
}

.login-form {
  padding: 0 32rpx 32rpx;
  background: #ffffff !important;
}

.login-tip {
  background: rgba(0, 123, 255, 0.1);
  border: 2rpx solid rgba(0, 123, 255, 0.2);
  border-radius: var(--radius);
  padding: 24rpx;
  margin-bottom: 32rpx;
  text-align: center;
  color: #007bff;
  font-size: 26rpx;
}

.user-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 32rpx;
  background: #f8f9fa !important;
  border-radius: var(--radius-large);
  border: 2rpx solid var(--border-color);
}

.user-preview .avatar,
.user-preview .avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.preview-name {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 600;
}



/* 响应式调整 */
@media (max-width: 750rpx) {
  .top-bar {
    padding: 24rpx;
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .user-section {
    align-self: flex-end;
  }

  .main-functions {
    padding: 24rpx;
  }

  .function-card {
    padding: 24rpx;
  }

  .quick-join-section {
    padding: 24rpx;
  }
}
