// index_test.js - 超简单测试版本
Page({
  data: {
    nickname: '',
    loading: false,
    loadingText: ''
  },

  onNicknameInput: function(e) {
    this.setData({
      nickname: e.detail.value
    })
  },

  testAPI: function() {
    this.setData({
      loading: true,
      loadingText: '测试API连接中...'
    })

    const app = getApp()
    wx.request({
      url: app.globalData.serverUrl + '/api/libraries',
      method: 'GET',
      success: (res) => {
        this.setData({
          loading: false
        })
        console.log('API测试成功:', res.data)
        wx.showModal({
          title: 'API测试结果',
          content: '连接成功！获取到' + (res.data.data ? res.data.data.length : 0) + '个题库',
          showCancel: false
        })
      },
      fail: (err) => {
        this.setData({
          loading: false
        })
        console.error('API测试失败:', err)
        wx.showModal({
          title: 'API测试失败',
          content: '错误信息: ' + (err.errMsg || '网络连接失败'),
          showCancel: false
        })
      }
    })
  },

  testGame: function() {
    if (!this.data.nickname) {
      wx.showToast({
        title: '请先输入昵称',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '测试功能',
      content: '昵称: ' + this.data.nickname + '\n准备跳转到游戏页面',
      showCancel: false
    })
  }
})
