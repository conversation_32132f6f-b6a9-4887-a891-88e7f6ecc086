/* 登录功能测试页面样式 */
.test-container {
  padding: 32rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

/* 状态显示区域 */
.status-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
  padding-bottom: 16rpx;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.value.success {
  color: #52c41a;
  font-weight: 600;
}

.value.error {
  color: #ff4d4f;
  font-weight: 600;
}

.value.small {
  font-size: 24rpx;
  color: #999;
}

/* 测试按钮区域 */
.test-actions {
  margin-bottom: 32rpx;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-btn::after {
  border: none;
}

.test-btn.primary {
  background: #1890ff;
  color: #fff;
}

.test-btn.primary:active {
  background: #096dd9;
}

.test-btn.secondary {
  background: #52c41a;
  color: #fff;
}

.test-btn.secondary:active {
  background: #389e0d;
}

.test-btn.warning {
  background: #faad14;
  color: #fff;
}

.test-btn.warning:active {
  background: #d48806;
}

.test-btn.danger {
  background: #ff4d4f;
  color: #fff;
}

.test-btn.danger:active {
  background: #cf1322;
}

.test-btn[disabled] {
  background: #d9d9d9 !important;
  color: #999 !important;
}

/* 日志显示区域 */
.log-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.log-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
  padding-bottom: 16rpx;
}

.log-content {
  height: 400rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 24rpx;
  background: #fafafa;
}

.log-item {
  display: flex;
  margin-bottom: 12rpx;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.log-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.log-time {
  font-size: 24rpx;
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
  margin-right: 16rpx;
}

.log-message {
  font-size: 26rpx;
  flex: 1;
  word-break: break-all;
}

.log-message.info {
  color: #333;
}

.log-message.success {
  color: #52c41a;
  font-weight: 500;
}

.log-message.warning {
  color: #faad14;
  font-weight: 500;
}

.log-message.error {
  color: #ff4d4f;
  font-weight: 500;
}

.clear-btn {
  width: 100%;
  height: 64rpx;
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.clear-btn::after {
  border: none;
}

.clear-btn:active {
  background: #e6e6e6;
}
