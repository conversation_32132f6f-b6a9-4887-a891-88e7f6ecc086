// index.js - 完整版本
const app = getApp()

Page({
  data: {
    userInfo: {},
    tempNickname: '',
    tempAvatarUrl: '',
    roomId: '',
    loading: false,
    loadingText: '',
    showLoginModal: false,
    pendingAction: null, // 存储待执行的操作
    openid: '', // 用户唯一标识
    loginLoading: false // 登录加载状态
  },

  onLoad() {
    // 从本地存储获取用户信息
    const savedUserInfo = wx.getStorageSync('userInfo')
    const savedOpenid = wx.getStorageSync('openid')

    if (savedUserInfo && savedUserInfo.nickName && savedOpenid) {
      this.setData({
        userInfo: savedUserInfo,
        openid: savedOpenid
      })
    }
  },

  // 选择头像
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    this.setData({
      tempAvatarUrl: avatarUrl
    })
    console.log('选择头像:', avatarUrl)
  },

  // 输入昵称
  onNicknameInput(e) {
    this.setData({
      tempNickname: e.detail.value
    })
  },

  // 确认用户信息
  async confirmUserInfo() {
    if (!this.data.tempNickname.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    this.setData({
      loginLoading: true
    })

    try {
      // 1. 先获取微信登录凭证和openid
      const openid = await this.getOpenId()

      if (!openid) {
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
        return
      }

      // 2. 构建用户信息
      const userInfo = {
        openid: openid,
        nickName: this.data.tempNickname.trim(),
        avatarUrl: this.data.tempAvatarUrl || '',
        level: 1,
        score: 0,
        createTime: new Date().toISOString()
      }

      // 3. 保存到本地存储
      wx.setStorageSync('userInfo', userInfo)
      wx.setStorageSync('openid', openid)

      // 4. 保存到后端数据库
      await this.saveUserToDatabase(userInfo)

      // 5. 更新页面状态
      this.setData({
        userInfo: userInfo,
        openid: openid,
        tempNickname: '',
        tempAvatarUrl: '',
        showLoginModal: false,
        loginLoading: false
      })

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 6. 执行待处理的操作
      if (this.data.pendingAction) {
        setTimeout(() => {
          this.executePendingAction()
        }, 1000)
      }

    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
      this.setData({
        loginLoading: false
      })
    }
  },

  // 获取用户openid
  getOpenId() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 发送code到后端获取openid
            wx.request({
              url: `${getApp().globalData.serverUrl}/api/auth/login`,
              method: 'POST',
              data: {
                code: res.code
              },
              success: (response) => {
                if (response.data.success && response.data.data.openid) {
                  resolve(response.data.data.openid)
                } else {
                  console.error('获取openid失败:', response.data.message)
                  reject(new Error(response.data.message || '获取openid失败'))
                }
              },
              fail: (error) => {
                console.error('请求失败:', error)
                reject(error)
              }
            })
          } else {
            console.error('wx.login失败:', res.errMsg)
            reject(new Error(res.errMsg))
          }
        },
        fail: (error) => {
          console.error('wx.login调用失败:', error)
          reject(error)
        }
      })
    })
  },

  // 保存用户信息到数据库
  saveUserToDatabase(userInfo) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${getApp().globalData.serverUrl}/api/user/save`,
        method: 'POST',
        data: userInfo,
        success: (res) => {
          if (res.data.success) {
            console.log('用户信息保存成功:', res.data)
            resolve(res.data)
          } else {
            console.error('用户信息保存失败:', res.data.message)
            reject(new Error(res.data.message))
          }
        },
        fail: (err) => {
          console.error('用户信息保存请求失败:', err)
          reject(err)
        }
      })
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认操作',
      content: '确定要更换用户信息吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('openid')

          this.setData({
            userInfo: {},
            openid: '',
            showLoginModal: true
          })
        }
      }
    })
  },

  // 显示登录弹窗
  showLoginModal() {
    this.setData({
      showLoginModal: true
    })
  },

  // 隐藏登录弹窗
  hideLoginModal() {
    this.setData({
      showLoginModal: false,
      pendingAction: null
    })
  },

  // 检查登录状态
  checkLoginStatus(action) {
    if (this.data.userInfo.nickName) {
      return true
    } else {
      this.setData({
        pendingAction: action,
        showLoginModal: true
      })
      return false
    }
  },

  // 执行待处理的操作
  executePendingAction() {
    const action = this.data.pendingAction
    this.setData({ pendingAction: null })

    switch (action) {
      case 'createRoom':
        this.createRoom()
        break
      case 'joinRoom':
        this.showJoinRoomModal()
        break
      case 'library':
        this.goToLibrary()
        break
      case 'history':
        this.goToHistory()
        break
    }
  },

  // 处理创建房间
  handleCreateRoom() {
    if (this.checkLoginStatus('createRoom')) {
      this.createRoom()
    }
  },

  // 处理加入房间
  handleJoinRoom() {
    if (this.checkLoginStatus('joinRoom')) {
      this.showJoinRoomModal()
    }
  },

  // 处理题库管理
  handleLibrary() {
    if (this.checkLoginStatus('library')) {
      this.goToLibrary()
    }
  },

  // 处理游戏历史
  handleHistory() {
    if (this.checkLoginStatus('history')) {
      this.goToHistory()
    }
  },

  // 快速加入房间
  handleQuickJoin() {
    if (this.checkLoginStatus('quickJoin')) {
      this.joinRoom()
    }
  },

  // 输入房间号
  onRoomIdInput(e) {
    this.setData({
      roomId: e.detail.value
    })
  },

  // 创建房间
  createRoom() {
    // 跳转到题库选择页面
    wx.navigateTo({
      url: `/pages/library/library?nickname=${this.data.userInfo.nickName}&avatarUrl=${this.data.userInfo.avatarUrl}`
    })
  },

  // 通过房间号加入房间
  joinRoomById(roomId) {
    this.setData({
      roomId: roomId
    })
    this.joinRoom()
  },

  // 加入房间
  joinRoom() {
    if (!this.data.roomId || this.data.roomId.length !== 4) {
      wx.showToast({
        title: '请输入4位房间号',
        icon: 'none'
      })
      return
    }

    this.setData({
      loading: true,
      loadingText: '正在加入房间...'
    })

    // 检查房间是否存在
    wx.request({
      url: app.globalData.serverUrl + '/api/room/' + this.data.roomId,
      method: 'GET',
      success: (res) => {
        this.setData({
          loading: false
        })

        if (res.data.success) {
          const room = res.data.data.room
          const players = res.data.data.players

          if (players.length >= 2) {
            wx.showToast({
              title: '房间已满',
              icon: 'none'
            })
            return
          }

          // 跳转到游戏页面
          wx.navigateTo({
            url: `/pages/game/game?roomId=${this.data.roomId}&nickname=${this.data.userInfo.nickName}&avatarUrl=${this.data.userInfo.avatarUrl}&libraryId=${room.library_id}&isHost=false`
          })
        } else {
          wx.showToast({
            title: res.data.message || '房间不存在',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        this.setData({
          loading: false
        })
        console.error('加入房间失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 测试API连接
  testAPI() {
    wx.showLoading({
      title: '测试中...'
    })

    wx.request({
      url: app.globalData.serverUrl + '/api/libraries',
      method: 'GET',
      success: (res) => {
        wx.hideLoading()
        console.log('API测试成功:', res.data)
        wx.showModal({
          title: 'API测试结果',
          content: `连接成功！获取到${res.data.data ? res.data.data.length : 0}个题库`,
          showCancel: false
        })
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('API测试失败:', err)
        wx.showModal({
          title: 'API测试失败',
          content: `错误信息: ${err.errMsg || '网络连接失败'}`,
          showCancel: false
        })
      }
    })
  },

  // 测试游戏界面
  testGame() {
    const nickname = this.data.userInfo.nickName || '测试玩家'
    const avatarUrl = this.data.userInfo.avatarUrl || ''

    // 直接跳转到游戏页面进行UI测试
    wx.navigateTo({
      url: `/pages/game/game?roomId=1234&nickname=${nickname}&avatarUrl=${avatarUrl}&libraryId=basic&isHost=true`
    })
  }
})
