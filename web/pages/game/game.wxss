/**game.wxss**/
page {
  background: var(--bg-primary);
}

.container {
  min-height: 100vh;
  padding: 40rpx;
  box-sizing: border-box;
}

/* 等待房间样式 */
.waiting-room {
  text-align: center;
}

.room-info {
  background: var(--bg-secondary);
  border: 6rpx solid var(--primary-color);
  border-radius: var(--border-radius);
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.room-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 20rpx;
}

.room-subtitle {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
}

.players-info {
  margin-bottom: 40rpx;
}

.player-list {
  background: var(--bg-secondary);
  border: 4rpx solid var(--bg-dark);
  border-radius: var(--border-radius);
  padding: 30rpx;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #F0F0F0;
}

.player-item:last-child {
  border-bottom: none;
}

.player-item.empty {
  opacity: 0.6;
}

.player-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.player-name {
  flex: 1;
  font-size: 32rpx;
  color: var(--text-primary);
}

.player-role {
  font-size: 24rpx;
  color: var(--primary-color);
}

.start-section {
  margin-bottom: 40rpx;
}

.waiting-tip {
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 游戏进行中样式 */
.game-progress {
  margin-bottom: 40rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.question-card {
  background: var(--bg-secondary);
  border: 6rpx solid var(--bg-dark);
  border-radius: var(--border-radius);
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: var(--shadow);
}

.question-text {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-primary);
  border: 4rpx solid var(--text-secondary);
  border-radius: var(--border-radius);
  padding: 30rpx;
  transition: all 0.2s ease;
}

.option-item:active {
  transform: translateY(2rpx);
}

.option-item.selected {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
}

.option-item.my-answer {
  border-color: var(--success-color);
  background: var(--success-color);
}

.option-text {
  font-size: 32rpx;
  font-weight: 500;
}

.option-indicator {
  font-size: 24rpx;
  font-weight: bold;
}

/* 等待答题样式 */
.waiting-answer {
  text-align: center;
  padding: 60rpx 0;
}

.waiting-text {
  display: block;
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 30rpx;
}

.waiting-dots {
  display: flex;
  justify-content: center;
  gap: 10rpx;
}

.dot {
  font-size: 20rpx;
  color: var(--primary-color);
  animation: blink 1.5s infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.3s;
}

.dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* 题目结果样式 */
.question-result {
  text-align: center;
  padding: 40rpx;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  margin-bottom: 40rpx;
}

.result-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.result-icon.big {
  font-size: 120rpx;
}

.result-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.result-icon.correct + .result-text {
  color: var(--success-color);
}

.result-icon.wrong + .result-text {
  color: var(--warning-color);
}

.answers-comparison {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.answer-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background: var(--bg-primary);
  border-radius: 10rpx;
}

.answer-player {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.answer-text {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 游戏结束样式 */
.game-finished {
  text-align: center;
}

.final-result {
  background: var(--bg-secondary);
  border: 6rpx solid var(--success-color);
  border-radius: var(--border-radius);
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 30rpx;
}

.result-score {
  margin-bottom: 30rpx;
}

.score-text {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.score-detail {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
}

.result-comment {
  font-size: 32rpx;
  color: var(--text-primary);
  line-height: 1.4;
}

.game-actions {
  display: flex;
  gap: 20rpx;
}

.game-actions .pixel-btn {
  flex: 1;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 40rpx;
  border-radius: var(--border-radius);
  z-index: 1000;
}
