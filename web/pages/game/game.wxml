<!--game.wxml-->
<view class="container">
    
    <!-- 等待状态 -->
    <view class="waiting-room" wx:if="{{gameStatus === 'waiting'}}">
      <view class="room-info">
        <text class="room-title">房间号: {{roomId}}</text>
        <text class="room-subtitle">分享给朋友一起来玩吧！</text>
      </view>

      <view class="players-info">
        <view class="subtitle">玩家列表 ({{players.length}}/2)</view>
        <view class="player-list">
          <view class="player-item" wx:for="{{players}}" wx:key="player_id">
            <text class="player-icon">👤</text>
            <text class="player-name">{{item.nickname}}</text>
            <text class="player-role" wx:if="{{item.player_id === playerId}}">（你）</text>
          </view>
          
          <!-- 空位显示 -->
          <view class="player-item empty" wx:if="{{players.length < 2}}">
            <text class="player-icon">⏳</text>
            <text class="player-name">等待玩家加入...</text>
          </view>
        </view>
      </view>

      <!-- 开始游戏按钮 -->
      <view class="start-section" wx:if="{{isHost && players.length === 2}}">
        <button class="pixel-btn" bindtap="startGame">
          🚀 开始游戏
        </button>
      </view>

      <!-- 等待提示 -->
      <view class="waiting-tip" wx:if="{{!isHost || players.length < 2}}">
        <text wx:if="{{players.length < 2}}">等待另一位玩家加入...</text>
        <text wx:else>等待房主开始游戏...</text>
      </view>
    </view>

    <!-- 游戏进行中 -->
    <view class="game-playing" wx:if="{{gameStatus === 'playing'}}">
      <!-- 游戏进度 -->
      <view class="game-progress">
        <view class="progress-info">
          <text>第 {{currentQuestion + 1}} / 5 题</text>
          <text>当前得分: {{currentScore}}</text>
        </view>
        <view class="pixel-progress">
          <view class="pixel-progress-bar" style="width: {{(currentQuestion / 5) * 100}}%"></view>
        </view>
      </view>

      <!-- 题目显示 -->
      <view class="question-card" wx:if="{{question}}">
        <view class="question-text">{{question.question}}</view>
        <view class="options-list">
          <view 
            class="option-item {{selectedAnswer === index ? 'selected' : ''}} {{showResult ? (selectedAnswer === index ? 'my-answer' : '') : ''}}"
            wx:for="{{question.options}}" 
            wx:key="index"
            bindtap="selectOption"
            data-index="{{index}}"
          >
            <text class="option-text">{{item}}</text>
            <view class="option-indicator" wx:if="{{showResult && selectedAnswer === index}}">✓</view>
          </view>
        </view>
      </view>

      <!-- 等待对方答题 -->
      <view class="waiting-answer" wx:if="{{selectedAnswer !== null && !showResult}}">
        <text class="waiting-text">等待对方选择答案...</text>
        <view class="waiting-dots">
          <text class="dot">●</text>
          <text class="dot">●</text>
          <text class="dot">●</text>
        </view>
      </view>

      <!-- 题目结果 -->
      <view class="question-result" wx:if="{{showResult}}">
        <view class="result-icon {{questionResult.isCorrect ? 'correct' : 'wrong'}}">
          {{questionResult.isCorrect ? '🎉' : '😅'}}
        </view>
        <view class="result-text">
          {{questionResult.isCorrect ? '默契满分！' : '想法不同呢~'}}
        </view>
        <view class="answers-comparison">
          <view class="answer-item" wx:for="{{questionResult.answers}}" wx:key="playerId">
            <text class="answer-player">{{getPlayerName(item.playerId)}}</text>
            <text class="answer-text">{{question.options[item.answer]}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 游戏结束 -->
    <view class="game-finished" wx:if="{{gameStatus === 'finished'}}">
      <view class="final-result">
        <view class="result-icon big">
          {{finalResult.percentage >= 80 ? '🏆' : finalResult.percentage >= 60 ? '👏' : '💪'}}
        </view>
        <view class="result-title">游戏结束！</view>
        <view class="result-score">
          <text class="score-text">默契度: {{finalResult.percentage}}%</text>
          <text class="score-detail">答对 {{finalResult.finalScore}} / {{finalResult.totalQuestions}} 题</text>
        </view>
        <view class="result-comment">
          <text wx:if="{{finalResult.percentage >= 80}}">你们真是心有灵犀！</text>
          <text wx:elif="{{finalResult.percentage >= 60}}">默契度不错，继续加油！</text>
          <text wx:else>多了解彼此，默契会更好哦~</text>
        </view>
      </view>

      <view class="game-actions">
        <button class="pixel-btn secondary" bindtap="backToHome">
          🏠 返回首页
        </button>
        <button class="pixel-btn" bindtap="playAgain">
          🔄 再玩一局
        </button>
      </view>
    </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>{{loadingText}}</text>
  </view>
</view>
