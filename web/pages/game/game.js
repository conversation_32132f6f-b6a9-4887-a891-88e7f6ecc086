// game.js
// const io = require('../../utils/socket.io.js')

Page({
  data: {
    roomId: '',
    nickname: '',
    libraryId: '',
    isHost: false,
    playerId: '',
    
    gameStatus: 'waiting', // waiting, playing, finished
    players: [],
    
    // 游戏数据
    currentQuestion: 0,
    currentScore: 0,
    question: null,
    selectedAnswer: null,
    showResult: false,
    questionResult: null,
    finalResult: null,
    
    loading: false,
    loadingText: ''
  },

  socket: null,

  onLoad(options) {
    const playerId = this.generatePlayerId()

    this.setData({
      roomId: options.roomId || '',
      nickname: options.nickname || '',
      libraryId: options.libraryId || '',
      isHost: options.isHost === 'true',
      playerId: playerId
    })

    // 暂时不使用WebSocket，直接模拟游戏流程
    this.simulateGame()
  },

  onUnload() {
    // if (this.socket) {
    //   this.socket.disconnect()
    // }
  },

  // 模拟游戏流程（用于测试UI）
  simulateGame() {
    // 模拟玩家加入
    this.setData({
      players: [
        { player_id: this.data.playerId, nickname: this.data.nickname },
        { player_id: 'player_2', nickname: '测试玩家' }
      ]
    })

    // 如果是房主，3秒后自动开始游戏
    if (this.data.isHost) {
      setTimeout(() => {
        this.startSimulatedGame()
      }, 3000)
    }
  },

  // 开始模拟游戏
  startSimulatedGame() {
    const mockQuestion = {
      question_id: 1,
      question: "你最喜欢的颜色是？",
      options: ["红色", "蓝色", "绿色", "黄色"]
    }

    this.setData({
      gameStatus: 'playing',
      currentQuestion: 0,
      question: mockQuestion,
      selectedAnswer: null,
      showResult: false
    })
  },

  // 生成玩家ID
  generatePlayerId() {
    return 'player_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  },

  // 连接WebSocket (暂时禁用)
  connectSocket() {
    console.log('WebSocket连接已禁用，使用模拟模式')
    // this.setData({
    //   loading: true,
    //   loadingText: '连接服务器...'
    // })

    // try {
    //   this.socket = io('http://localhost:3000')
    //   // ... WebSocket逻辑
    // } catch (error) {
    //   console.error('Socket连接失败:', error)
    // }
  },

  // 设置游戏事件监听
  setupGameListeners() {
    // 玩家加入
    this.socket.on('playerJoined', (data) => {
      console.log('玩家加入:', data)
      this.setData({
        players: data.players
      })
    })

    // 房间准备就绪
    this.socket.on('roomReady', (data) => {
      console.log('房间准备就绪:', data)
      wx.showToast({
        title: '房间已满员！',
        icon: 'success'
      })
    })

    // 游戏开始
    this.socket.on('gameStarted', (data) => {
      console.log('游戏开始:', data)
      this.setData({
        gameStatus: 'playing',
        currentQuestion: data.currentQuestion,
        question: data.question,
        selectedAnswer: null,
        showResult: false
      })
    })

    // 下一题
    this.socket.on('nextQuestion', (data) => {
      console.log('下一题:', data)
      this.setData({
        currentQuestion: data.currentQuestion,
        question: data.question,
        selectedAnswer: null,
        showResult: false,
        questionResult: null
      })
    })

    // 有玩家答题
    this.socket.on('playerAnswered', (data) => {
      console.log('有玩家答题:', data)
      // 可以显示答题进度
    })

    // 题目结果
    this.socket.on('questionResult', (data) => {
      console.log('题目结果:', data)
      this.setData({
        showResult: true,
        questionResult: data,
        currentScore: data.currentScore
      })
    })

    // 游戏结束
    this.socket.on('gameFinished', (data) => {
      console.log('游戏结束:', data)
      this.setData({
        gameStatus: 'finished',
        finalResult: data
      })
      
      // 更新游戏统计
      this.updateGameStats(data)
    })
  },

  // 加入房间
  joinRoom() {
    this.socket.emit('joinRoom', {
      roomId: this.data.roomId,
      playerId: this.data.playerId,
      nickname: this.data.nickname
    })
  },

  // 开始游戏
  startGame() {
    if (!this.data.isHost) {
      wx.showToast({
        title: '只有房主可以开始游戏',
        icon: 'none'
      })
      return
    }

    if (this.data.players.length < 2) {
      wx.showToast({
        title: '需要2名玩家才能开始',
        icon: 'none'
      })
      return
    }

    // 直接开始模拟游戏
    this.startSimulatedGame()
  },

  // 选择答案
  selectOption(e) {
    if (this.data.selectedAnswer !== null) {
      return // 已经选择过了
    }

    const index = e.currentTarget.dataset.index
    this.setData({
      selectedAnswer: index
    })

    // 模拟提交答案
    setTimeout(() => {
      this.simulateQuestionResult(index)
    }, 2000)
  },

  // 模拟题目结果
  simulateQuestionResult(playerAnswer) {
    const isCorrect = Math.random() > 0.5 // 随机结果

    this.setData({
      showResult: true,
      questionResult: {
        isCorrect: isCorrect,
        answers: [
          { playerId: this.data.playerId, answer: playerAnswer },
          { playerId: 'player_2', answer: isCorrect ? playerAnswer : (playerAnswer + 1) % 4 }
        ]
      },
      currentScore: this.data.currentScore + (isCorrect ? 1 : 0)
    })

    // 3秒后进入下一题或结束游戏
    setTimeout(() => {
      if (this.data.currentQuestion >= 4) {
        this.finishGame()
      } else {
        this.nextQuestion()
      }
    }, 3000)
  },

  // 下一题
  nextQuestion() {
    const nextQuestionIndex = this.data.currentQuestion + 1
    const mockQuestion = {
      question_id: nextQuestionIndex + 1,
      question: `第${nextQuestionIndex + 1}题：你最喜欢的季节是？`,
      options: ["春天", "夏天", "秋天", "冬天"]
    }

    this.setData({
      currentQuestion: nextQuestionIndex,
      question: mockQuestion,
      selectedAnswer: null,
      showResult: false,
      questionResult: null
    })
  },

  // 结束游戏
  finishGame() {
    this.setData({
      gameStatus: 'finished',
      finalResult: {
        finalScore: this.data.currentScore,
        totalQuestions: 5,
        percentage: Math.round((this.data.currentScore / 5) * 100)
      }
    })
  },

  // 获取玩家姓名
  getPlayerName(playerId) {
    const player = this.data.players.find(p => p.player_id === playerId)
    if (player) {
      return player.player_id === this.data.playerId ? '你' : player.nickname
    }
    return '未知玩家'
  },

  // 更新游戏统计
  updateGameStats(result) {
    const gameStats = wx.getStorageSync('gameStats') || {
      gamesPlayed: 0,
      totalScore: 0,
      averageScore: 0
    }

    gameStats.gamesPlayed += 1
    gameStats.totalScore += result.percentage
    gameStats.averageScore = Math.round(gameStats.totalScore / gameStats.gamesPlayed)

    wx.setStorageSync('gameStats', gameStats)
  },

  // 返回首页
  backToHome() {
    wx.reLaunch({
      url: '/pages/index/index'
    })
  },

  // 再玩一局
  playAgain() {
    wx.redirectTo({
      url: `/pages/library/library?nickname=${this.data.nickname}`
    })
  }
})
