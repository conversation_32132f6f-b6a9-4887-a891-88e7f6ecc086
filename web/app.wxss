/**app.wxss - 简约白色主题**/

/* 基础样式 */
page {
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  min-height: 100vh;
}

/* 色彩方案 */
:root {
  /* 主色调 */
  --primary: #007bff;
  --secondary: #6c757d;
  --accent: #17a2b8;
  --success: #28a745;
  --warning: #ffc107;
  --danger: #dc3545;

  /* 文字颜色 */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --text-white: #ffffff;

  /* 背景色 */
  --bg-white: #ffffff;
  --bg-light: #f8f9fa;
  --bg-card: #ffffff;

  /* 边框和阴影 */
  --border-color: #e9ecef;
  --shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);

  /* 圆角 */
  --radius: 16rpx;
  --radius-large: 24rpx;
}

/* 通用布局 */
.container {
  padding: 32rpx;
  box-sizing: border-box;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: var(--bg-card);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-large);
  padding: 48rpx;
  margin: 24rpx 0;
  box-shadow: var(--shadow);
}

/* 按钮样式 */
.btn {
  padding: 32rpx 48rpx;
  border-radius: var(--radius);
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  border: none;
  margin: 16rpx 0;
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
}

.btn.primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: var(--text-white);
  box-shadow: 0 4rpx 16rpx rgba(0, 123, 255, 0.3);
}

.btn.secondary {
  background: var(--bg-white);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.btn.accent {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: var(--text-white);
  box-shadow: 0 4rpx 16rpx rgba(23, 162, 184, 0.3);
}

.btn.success {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: var(--text-white);
  box-shadow: 0 4rpx 16rpx rgba(40, 167, 69, 0.3);
}

.btn:active {
  transform: translateY(2rpx) scale(0.98);
}

.btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 32rpx 36rpx;
  background: var(--bg-white);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius);
  font-size: 36rpx;
  color: var(--text-primary);
  box-sizing: border-box;
  transition: all 0.3s ease;
  min-height: 96rpx;
}

.input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 6rpx rgba(0, 123, 255, 0.1);
  outline: none;
}

.input::placeholder {
  color: var(--text-muted);
}

/* 头像样式 */
.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid var(--bg-white);
  box-shadow: var(--shadow);
}

.avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid var(--border-color);
  box-shadow: var(--shadow-light);
  background: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: var(--text-secondary);
}

/* 标题样式 */
.title {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  margin: 32rpx 0;
}

.subtitle {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin: 24rpx 0;
}

/* 文字样式 */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

/* 间距工具类 */
.mt-20 { margin-top: 20rpx; }
.mt-40 { margin-top: 40rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-40 { margin-bottom: 40rpx; }

/* 加载状态 */
.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--bg-white);
  color: var(--text-primary);
  padding: 40rpx;
  border-radius: var(--radius-large);
  box-shadow: var(--shadow);
  z-index: 9999;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}
