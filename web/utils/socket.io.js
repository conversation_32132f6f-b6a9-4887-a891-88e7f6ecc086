// socket.io.js - 简化版WebSocket客户端
class SocketIO {
  constructor(url) {
    this.url = url.replace('http', 'ws')
    this.socket = null
    this.connected = false
    this.listeners = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.autoConnect = false
  }

  connect() {
    return new Promise((resolve, reject) => {
      try {
        console.log('尝试连接WebSocket:', this.url)

        this.socket = wx.connectSocket({
          url: this.url,
          success: () => {
            console.log('WebSocket连接请求发送成功')
          },
          fail: (error) => {
            console.error('WebSocket连接请求失败:', error)
            reject(error)
          }
        })

        this.socket.onOpen((res) => {
          console.log('WebSocket连接成功', res)
          this.connected = true
          this.reconnectAttempts = 0
          this.triggerEvent('connect')
          resolve()
        })

        this.socket.onMessage((res) => {
          try {
            console.log('收到WebSocket消息:', res.data)
            const data = JSON.parse(res.data)
            if (data.type && this.listeners.has(data.type)) {
              const callbacks = this.listeners.get(data.type)
              callbacks.forEach(callback => callback(data.data))
            }
          } catch (error) {
            console.error('解析消息失败:', error)
          }
        })

        this.socket.onClose((res) => {
          console.log('WebSocket连接关闭', res)
          this.connected = false
          this.triggerEvent('disconnect')
          if (this.autoConnect) {
            this.handleReconnect()
          }
        })

        this.socket.onError((error) => {
          console.error('WebSocket错误:', error)
          this.triggerEvent('error', error)
          reject(error)
        })

      } catch (error) {
        console.error('创建WebSocket失败:', error)
        reject(error)
      }
    })
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('重连失败:', error)
        })
      }, 2000 * this.reconnectAttempts)
    }
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  emit(event, data) {
    if (this.connected && this.socket) {
      const message = JSON.stringify({
        type: event,
        data: data
      })
      console.log('发送WebSocket消息:', message)
      this.socket.send({
        data: message,
        success: () => {
          console.log('消息发送成功')
        },
        fail: (error) => {
          console.error('消息发送失败:', error)
        }
      })
    } else {
      console.warn('Socket未连接，无法发送消息:', event, data)
    }
  }

  triggerEvent(event, data) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      callbacks.forEach(callback => callback(data))
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.close()
      this.connected = false
    }
  }
}

// 创建Socket.io实例的工厂函数
function io(url) {
  const socket = new SocketIO(url)
  
  // 自动连接
  socket.connect().catch(error => {
    console.error('Socket连接失败:', error)
  })
  
  return socket
}

module.exports = io
