# 默契挑战游戏 - 登录系统配置说明

## 🔧 系统架构

### 用户唯一标识方案
- **主要标识**: `openid` - 微信用户在当前小程序的唯一标识
- **备用标识**: `unionid` - 同一开放平台下的统一标识（需要绑定开放平台）
- **本地存储**: 用户信息和openid同时保存在本地和服务器

### 登录流程
1. 用户点击登录 → 显示授权弹窗
2. 用户选择头像（可选）+ 输入昵称
3. 调用 `wx.login()` 获取临时登录凭证 `code`
4. 发送 `code` 到后端，换取 `openid`
5. 保存用户信息到本地存储和服务器数据库
6. 完成登录，执行后续操作

## 📋 配置步骤

### 1. 微信小程序配置

#### 获取 AppID 和 AppSecret
1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入小程序管理后台
3. 开发 → 开发管理 → 开发设置
4. 复制 `AppID` 和 `AppSecret`

#### 配置服务器域名
1. 开发 → 开发管理 → 开发设置 → 服务器域名
2. 添加你的服务器域名到 `request合法域名`

### 2. 后端配置

#### 环境变量设置
```bash
# 复制配置文件
cp server/.env.example server/.env

# 编辑配置文件
vim server/.env
```

```env
# 填入真实的微信小程序配置
WX_APPID=wx1234567890abcdef
WX_SECRET=abcdef1234567890abcdef1234567890
PORT=3000
```

#### 安装依赖
```bash
cd server
npm install
```

#### 启动服务器
```bash
npm start
# 或开发模式
npm run dev
```

### 3. 前端配置

#### 修改服务器地址
编辑 `web/app.js`:
```javascript
globalData: {
  serverUrl: 'https://your-domain.com' // 改为你的服务器地址
}
```

## 🔐 安全注意事项

### 1. AppSecret 安全
- ⚠️ **绝对不要**将 `AppSecret` 放在前端代码中
- ✅ 只在后端服务器中使用 `AppSecret`
- ✅ 使用环境变量管理敏感配置

### 2. Session Key 管理
- ⚠️ **不要**将 `session_key` 返回给前端
- ✅ 在后端保存 `session_key` 用于解密用户数据

### 3. 数据传输
- ✅ 生产环境使用 HTTPS
- ✅ 验证请求来源和参数

## 📊 数据库结构

### 用户表 (users.json)
```json
{
  "openid_123": {
    "openid": "openid_123",
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "level": 1,
    "score": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "stats": {
      "totalGames": 0,
      "totalScore": 0,
      "averageScore": 0
    }
  }
}
```

## 🚀 API 接口

### 1. 微信登录
```
POST /api/auth/login
Content-Type: application/json

{
  "code": "微信登录凭证"
}

Response:
{
  "success": true,
  "data": {
    "openid": "用户openid",
    "unionid": "用户unionid" // 可选
  }
}
```

### 2. 保存用户信息
```
POST /api/user/save
Content-Type: application/json

{
  "openid": "用户openid",
  "nickName": "用户昵称",
  "avatarUrl": "头像URL",
  "level": 1,
  "score": 0
}
```

### 3. 获取用户信息
```
GET /api/user/:openid

Response:
{
  "success": true,
  "data": {
    "openid": "用户openid",
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    // ... 其他用户信息
  }
}
```

## 🐛 常见问题

### 1. 登录按钮无法点击
**原因**: 昵称为空或登录状态异常
**解决**: 检查 `tempNickname` 和 `loginLoading` 状态

### 2. 获取 openid 失败
**原因**: AppID/AppSecret 配置错误或网络问题
**解决**: 
- 检查微信小程序配置
- 确认服务器网络连接
- 查看后端日志

### 3. 用户信息保存失败
**原因**: 数据库权限或网络问题
**解决**:
- 检查数据目录权限
- 确认服务器磁盘空间
- 查看错误日志

## 🔄 数据同步策略

### 本地优先
- 优先使用本地存储的用户信息
- 定期同步到服务器
- 离线时仍可使用基本功能

### 服务器备份
- 所有用户数据在服务器备份
- 支持跨设备数据同步
- 防止数据丢失

## 📈 后续优化方向

1. **数据库升级**: 从JSON文件升级到SQLite/MySQL
2. **缓存机制**: 添加Redis缓存提升性能
3. **用户统计**: 完善用户游戏数据统计
4. **社交功能**: 基于openid实现好友系统
5. **数据分析**: 用户行为分析和推荐系统
