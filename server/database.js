const Database = require('better-sqlite3');
const path = require('path');

class GameDatabase {
  constructor() {
    this.dbPath = path.join(__dirname, 'game.db');
    try {
      this.db = new Database(this.dbPath);
      console.log('SQLite数据库连接成功');
      this.initTables();
    } catch (err) {
      console.error('数据库连接失败:', err.message);
      throw err;
    }
  }

  // 初始化数据库表
  initTables() {
    const tables = [
      // 房间表
      `CREATE TABLE IF NOT EXISTS rooms (
        room_id TEXT PRIMARY KEY,
        library_id TEXT NOT NULL,
        status TEXT DEFAULT 'waiting',
        current_question INTEGER DEFAULT 0,
        score INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 玩家表
      `CREATE TABLE IF NOT EXISTS players (
        player_id TEXT PRIMARY KEY,
        room_id TEXT,
        nickname TEXT,
        socket_id TEXT,
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOR<PERSON>GN KEY (room_id) REFERENCES rooms (room_id)
      )`,
      
      // 答案表
      `CREATE TABLE IF NOT EXISTS answers (
        room_id TEXT,
        player_id TEXT,
        question_index INTEGER,
        answer TEXT,
        answered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (room_id, player_id, question_index),
        FOREIGN KEY (room_id) REFERENCES rooms (room_id),
        FOREIGN KEY (player_id) REFERENCES players (player_id)
      )`,
      
      // 题库表
      `CREATE TABLE IF NOT EXISTS question_libraries (
        library_id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        unlocked INTEGER DEFAULT 0
      )`,
      
      // 题目表
      `CREATE TABLE IF NOT EXISTS questions (
        question_id INTEGER PRIMARY KEY AUTOINCREMENT,
        library_id TEXT,
        question TEXT NOT NULL,
        options TEXT NOT NULL,
        FOREIGN KEY (library_id) REFERENCES question_libraries (library_id)
      )`
    ];

    tables.forEach((sql, index) => {
      try {
        this.db.exec(sql);
        console.log(`表${index + 1}创建成功`);
      } catch (err) {
        console.error(`创建表${index + 1}失败:`, err.message);
      }
    });

    // 初始化题库数据
    this.initQuestionLibraries();
  }

  // 初始化题库数据
  initQuestionLibraries() {
    const libraries = [
      {
        id: 'basic',
        name: '基础默契',
        description: '测试基本的生活喜好',
        icon: '🎯',
        unlocked: 1
      },
      {
        id: 'food',
        name: '美食默契',
        description: '关于美食的喜好测试',
        icon: '🍕',
        unlocked: 0
      },
      {
        id: 'movie',
        name: '影视默契',
        description: '电影电视剧喜好测试',
        icon: '🎬',
        unlocked: 0
      }
    ];

    for (const library of libraries) {
      this.insertLibrary(library);
    }

    // 初始化题目数据
    this.initQuestions();
  }

  // 插入题库
  insertLibrary(library) {
    try {
      const stmt = this.db.prepare(`INSERT OR IGNORE INTO question_libraries 
                                   (library_id, name, description, icon, unlocked) 
                                   VALUES (?, ?, ?, ?, ?)`);
      const result = stmt.run(library.id, library.name, library.description, library.icon, library.unlocked);
      console.log(`题库 ${library.name} 初始化成功`);
      return result.lastInsertRowid;
    } catch (err) {
      console.error('插入题库失败:', err.message);
      throw err;
    }
  }

  // 初始化题目数据
  initQuestions() {
    const basicQuestions = [
      {
        question: "你最喜欢的颜色是？",
        options: ["红色", "蓝色", "绿色", "黄色"]
      },
      {
        question: "你最喜欢的季节是？",
        options: ["春天", "夏天", "秋天", "冬天"]
      },
      {
        question: "你最喜欢的运动是？",
        options: ["跑步", "游泳", "篮球", "足球"]
      },
      {
        question: "你最喜欢的音乐类型是？",
        options: ["流行", "摇滚", "古典", "民谣"]
      },
      {
        question: "你最喜欢的旅行方式是？",
        options: ["自驾游", "跟团游", "背包游", "度假村"]
      }
    ];

    const foodQuestions = [
      {
        question: "你最喜欢的菜系是？",
        options: ["川菜", "粤菜", "湘菜", "鲁菜"]
      },
      {
        question: "你最喜欢的饮品是？",
        options: ["咖啡", "茶", "果汁", "汽水"]
      },
      {
        question: "你最喜欢的甜品是？",
        options: ["蛋糕", "冰淇淋", "布丁", "巧克力"]
      },
      {
        question: "你最喜欢的早餐是？",
        options: ["包子", "面条", "面包", "粥"]
      },
      {
        question: "你最喜欢的零食是？",
        options: ["薯片", "坚果", "糖果", "饼干"]
      }
    ];

    const movieQuestions = [
      {
        question: "你最喜欢的电影类型是？",
        options: ["动作", "喜剧", "爱情", "科幻"]
      },
      {
        question: "你最喜欢的电视剧类型是？",
        options: ["古装", "现代", "悬疑", "喜剧"]
      },
      {
        question: "你最喜欢看电影的时间是？",
        options: ["上午", "下午", "晚上", "深夜"]
      },
      {
        question: "你最喜欢的观影方式是？",
        options: ["电影院", "家里", "平板", "手机"]
      },
      {
        question: "你最喜欢的演员类型是？",
        options: ["实力派", "偶像派", "喜剧派", "动作派"]
      }
    ];

    // 插入基础题库题目
    for (const q of basicQuestions) {
      this.insertQuestion('basic', q.question, JSON.stringify(q.options));
    }

    // 插入美食题库题目
    for (const q of foodQuestions) {
      this.insertQuestion('food', q.question, JSON.stringify(q.options));
    }

    // 插入影视题库题目
    for (const q of movieQuestions) {
      this.insertQuestion('movie', q.question, JSON.stringify(q.options));
    }
  }

  // 插入题目
  insertQuestion(libraryId, question, options) {
    try {
      const stmt = this.db.prepare(`INSERT OR IGNORE INTO questions (library_id, question, options) VALUES (?, ?, ?)`);
      const result = stmt.run(libraryId, question, options);
      return result.lastInsertRowid;
    } catch (err) {
      console.error('插入题目失败:', err.message);
      throw err;
    }
  }

  // 创建房间
  createRoom(roomId, libraryId) {
    try {
      const stmt = this.db.prepare(`INSERT INTO rooms (room_id, library_id) VALUES (?, ?)`);
      const result = stmt.run(roomId, libraryId);
      console.log(`房间 ${roomId} 创建成功`);
      return result.lastInsertRowid;
    } catch (err) {
      console.error('创建房间失败:', err.message);
      throw err;
    }
  }

  // 加入房间
  joinRoom(playerId, roomId, nickname, socketId) {
    try {
      const stmt = this.db.prepare(`INSERT OR REPLACE INTO players (player_id, room_id, nickname, socket_id) 
                                   VALUES (?, ?, ?, ?)`);
      const result = stmt.run(playerId, roomId, nickname, socketId);
      console.log(`玩家 ${nickname} 加入房间 ${roomId}`);
      return result.changes;
    } catch (err) {
      console.error('加入房间失败:', err.message);
      throw err;
    }
  }

  // 获取房间信息
  getRoom(roomId) {
    try {
      const stmt = this.db.prepare(`SELECT * FROM rooms WHERE room_id = ?`);
      return stmt.get(roomId);
    } catch (err) {
      console.error('获取房间失败:', err.message);
      throw err;
    }
  }

  // 获取房间内的玩家
  getRoomPlayers(roomId) {
    try {
      const stmt = this.db.prepare(`SELECT * FROM players WHERE room_id = ? ORDER BY joined_at`);
      return stmt.all(roomId);
    } catch (err) {
      console.error('获取房间玩家失败:', err.message);
      throw err;
    }
  }

  // 获取题库列表
  getLibraries() {
    try {
      const stmt = this.db.prepare(`SELECT * FROM question_libraries ORDER BY library_id`);
      return stmt.all();
    } catch (err) {
      console.error('获取题库失败:', err.message);
      throw err;
    }
  }

  // 获取题库的题目
  getQuestionsByLibrary(libraryId, limit = 5) {
    try {
      const stmt = this.db.prepare(`SELECT * FROM questions WHERE library_id = ? ORDER BY RANDOM() LIMIT ?`);
      const rows = stmt.all(libraryId, limit);
      // 解析options JSON字符串
      return rows.map(row => ({
        ...row,
        options: JSON.parse(row.options)
      }));
    } catch (err) {
      console.error('获取题目失败:', err.message);
      throw err;
    }
  }

  // 保存答案
  saveAnswer(roomId, playerId, questionIndex, answer) {
    try {
      const stmt = this.db.prepare(`INSERT OR REPLACE INTO answers (room_id, player_id, question_index, answer) 
                                   VALUES (?, ?, ?, ?)`);
      const result = stmt.run(roomId, playerId, questionIndex, answer);
      return result.changes;
    } catch (err) {
      console.error('保存答案失败:', err.message);
      throw err;
    }
  }

  // 获取当前题目的所有答案
  getQuestionAnswers(roomId, questionIndex) {
    try {
      const stmt = this.db.prepare(`SELECT * FROM answers WHERE room_id = ? AND question_index = ?`);
      return stmt.all(roomId, questionIndex);
    } catch (err) {
      console.error('获取答案失败:', err.message);
      throw err;
    }
  }

  // 更新房间状态
  updateRoomStatus(roomId, status, currentQuestion = null, score = null) {
    try {
      let sql = `UPDATE rooms SET status = ?`;
      let params = [status];
      
      if (currentQuestion !== null) {
        sql += `, current_question = ?`;
        params.push(currentQuestion);
      }
      
      if (score !== null) {
        sql += `, score = ?`;
        params.push(score);
      }
      
      sql += ` WHERE room_id = ?`;
      params.push(roomId);

      const stmt = this.db.prepare(sql);
      const result = stmt.run(...params);
      return result.changes;
    } catch (err) {
      console.error('更新房间状态失败:', err.message);
      throw err;
    }
  }

  // 清理过期数据
  cleanupExpiredData() {
    try {
      const transaction = this.db.transaction(() => {
        // 清理30分钟前创建的房间
        const cleanRooms = this.db.prepare(`DELETE FROM rooms WHERE created_at < datetime('now', '-30 minutes')`);
        const roomsDeleted = cleanRooms.run().changes;
        
        // 清理30分钟前加入的玩家
        const cleanPlayers = this.db.prepare(`DELETE FROM players WHERE joined_at < datetime('now', '-30 minutes')`);
        const playersDeleted = cleanPlayers.run().changes;
        
        // 清理孤立的答案记录
        const cleanAnswers = this.db.prepare(`DELETE FROM answers WHERE room_id NOT IN (SELECT room_id FROM rooms)`);
        const answersDeleted = cleanAnswers.run().changes;
        
        console.log(`清理了 ${roomsDeleted} 个过期房间, ${playersDeleted} 个过期玩家, ${answersDeleted} 个孤立答案`);
        
        return { roomsDeleted, playersDeleted, answersDeleted };
      });
      
      return transaction();
    } catch (err) {
      console.error('清理数据失败:', err.message);
      throw err;
    }
  }

  // 关闭数据库连接
  close() {
    try {
      this.db.close();
      console.log('数据库连接已关闭');
    } catch (err) {
      console.error('关闭数据库失败:', err.message);
      throw err;
    }
  }
}

module.exports = GameDatabase;
