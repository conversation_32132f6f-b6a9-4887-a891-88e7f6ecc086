const fs = require('fs-extra');
const path = require('path');

class GameDatabase {
  constructor() {
    this.dataDir = path.join(__dirname, 'data');
    this.roomsFile = path.join(this.dataDir, 'rooms.json');
    this.playersFile = path.join(this.dataDir, 'players.json');
    this.answersFile = path.join(this.dataDir, 'answers.json');
    this.usersFile = path.join(this.dataDir, 'users.json');

    this.initDatabase();
  }

  // 初始化数据库
  async initDatabase() {
    try {
      // 确保数据目录存在
      await fs.ensureDir(this.dataDir);
      
      // 初始化数据文件
      if (!await fs.pathExists(this.roomsFile)) {
        await fs.writeJson(this.roomsFile, {});
      }
      if (!await fs.pathExists(this.playersFile)) {
        await fs.writeJson(this.playersFile, {});
      }
      if (!await fs.pathExists(this.answersFile)) {
        await fs.writeJson(this.answersFile, {});
      }
      if (!await fs.pathExists(this.usersFile)) {
        await fs.writeJson(this.usersFile, {});
      }

      console.log('JSON数据库初始化成功');
    } catch (err) {
      console.error('数据库初始化失败:', err.message);
      throw err;
    }
  }

  // 获取题库列表
  getLibraries() {
    return [
      {
        library_id: 'basic',
        name: '基础默契',
        description: '测试基本的生活喜好',
        icon: '🎯',
        unlocked: 1
      },
      {
        library_id: 'food',
        name: '美食默契',
        description: '关于美食的喜好测试',
        icon: '🍕',
        unlocked: 0
      },
      {
        library_id: 'movie',
        name: '影视默契',
        description: '电影电视剧喜好测试',
        icon: '🎬',
        unlocked: 0
      }
    ];
  }

  // 获取题库的题目
  getQuestionsByLibrary(libraryId, limit = 5) {
    const allQuestions = {
      basic: [
        {
          question_id: 1,
          question: "你最喜欢的颜色是？",
          options: ["红色", "蓝色", "绿色", "黄色"]
        },
        {
          question_id: 2,
          question: "你最喜欢的季节是？",
          options: ["春天", "夏天", "秋天", "冬天"]
        },
        {
          question_id: 3,
          question: "你最喜欢的运动是？",
          options: ["跑步", "游泳", "篮球", "足球"]
        },
        {
          question_id: 4,
          question: "你最喜欢的音乐类型是？",
          options: ["流行", "摇滚", "古典", "民谣"]
        },
        {
          question_id: 5,
          question: "你最喜欢的旅行方式是？",
          options: ["自驾游", "跟团游", "背包游", "度假村"]
        }
      ],
      food: [
        {
          question_id: 6,
          question: "你最喜欢的菜系是？",
          options: ["川菜", "粤菜", "湘菜", "鲁菜"]
        },
        {
          question_id: 7,
          question: "你最喜欢的饮品是？",
          options: ["咖啡", "茶", "果汁", "汽水"]
        },
        {
          question_id: 8,
          question: "你最喜欢的甜品是？",
          options: ["蛋糕", "冰淇淋", "布丁", "巧克力"]
        },
        {
          question_id: 9,
          question: "你最喜欢的早餐是？",
          options: ["包子", "面条", "面包", "粥"]
        },
        {
          question_id: 10,
          question: "你最喜欢的零食是？",
          options: ["薯片", "坚果", "糖果", "饼干"]
        }
      ],
      movie: [
        {
          question_id: 11,
          question: "你最喜欢的电影类型是？",
          options: ["动作", "喜剧", "爱情", "科幻"]
        },
        {
          question_id: 12,
          question: "你最喜欢的电视剧类型是？",
          options: ["古装", "现代", "悬疑", "喜剧"]
        },
        {
          question_id: 13,
          question: "你最喜欢看电影的时间是？",
          options: ["上午", "下午", "晚上", "深夜"]
        },
        {
          question_id: 14,
          question: "你最喜欢的观影方式是？",
          options: ["电影院", "家里", "平板", "手机"]
        },
        {
          question_id: 15,
          question: "你最喜欢的演员类型是？",
          options: ["实力派", "偶像派", "喜剧派", "动作派"]
        }
      ]
    };

    const questions = allQuestions[libraryId] || [];
    // 随机选择题目
    const shuffled = questions.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, limit);
  }

  // 创建房间
  async createRoom(roomId, libraryId) {
    try {
      const rooms = await fs.readJson(this.roomsFile);
      rooms[roomId] = {
        room_id: roomId,
        library_id: libraryId,
        status: 'waiting',
        current_question: 0,
        score: 0,
        created_at: new Date().toISOString()
      };
      await fs.writeJson(this.roomsFile, rooms);
      console.log(`房间 ${roomId} 创建成功`);
      return true;
    } catch (err) {
      console.error('创建房间失败:', err.message);
      throw err;
    }
  }

  // 加入房间
  async joinRoom(playerId, roomId, nickname, socketId) {
    try {
      const players = await fs.readJson(this.playersFile);
      players[playerId] = {
        player_id: playerId,
        room_id: roomId,
        nickname: nickname,
        socket_id: socketId,
        joined_at: new Date().toISOString()
      };
      await fs.writeJson(this.playersFile, players);
      console.log(`玩家 ${nickname} 加入房间 ${roomId}`);
      return true;
    } catch (err) {
      console.error('加入房间失败:', err.message);
      throw err;
    }
  }

  // 获取房间信息
  async getRoom(roomId) {
    try {
      const rooms = await fs.readJson(this.roomsFile);
      return rooms[roomId] || null;
    } catch (err) {
      console.error('获取房间失败:', err.message);
      throw err;
    }
  }

  // 获取房间内的玩家
  async getRoomPlayers(roomId) {
    try {
      const players = await fs.readJson(this.playersFile);
      return Object.values(players).filter(player => player.room_id === roomId);
    } catch (err) {
      console.error('获取房间玩家失败:', err.message);
      throw err;
    }
  }

  // 保存答案
  async saveAnswer(roomId, playerId, questionIndex, answer) {
    try {
      const answers = await fs.readJson(this.answersFile);
      const key = `${roomId}_${playerId}_${questionIndex}`;
      answers[key] = {
        room_id: roomId,
        player_id: playerId,
        question_index: questionIndex,
        answer: answer,
        answered_at: new Date().toISOString()
      };
      await fs.writeJson(this.answersFile, answers);
      return true;
    } catch (err) {
      console.error('保存答案失败:', err.message);
      throw err;
    }
  }

  // 获取当前题目的所有答案
  async getQuestionAnswers(roomId, questionIndex) {
    try {
      const answers = await fs.readJson(this.answersFile);
      return Object.values(answers).filter(answer => 
        answer.room_id === roomId && answer.question_index === questionIndex
      );
    } catch (err) {
      console.error('获取答案失败:', err.message);
      throw err;
    }
  }

  // 更新房间状态
  async updateRoomStatus(roomId, status, currentQuestion = null, score = null) {
    try {
      const rooms = await fs.readJson(this.roomsFile);
      if (rooms[roomId]) {
        rooms[roomId].status = status;
        if (currentQuestion !== null) {
          rooms[roomId].current_question = currentQuestion;
        }
        if (score !== null) {
          rooms[roomId].score = score;
        }
        await fs.writeJson(this.roomsFile, rooms);
        return true;
      }
      return false;
    } catch (err) {
      console.error('更新房间状态失败:', err.message);
      throw err;
    }
  }

  // 清理过期数据
  async cleanupExpiredData() {
    try {
      const now = new Date();
      const thirtyMinutesAgo = new Date(now.getTime() - 30 * 60 * 1000);

      // 清理过期房间
      const rooms = await fs.readJson(this.roomsFile);
      const validRooms = {};
      let roomsDeleted = 0;
      
      for (const [roomId, room] of Object.entries(rooms)) {
        if (new Date(room.created_at) > thirtyMinutesAgo) {
          validRooms[roomId] = room;
        } else {
          roomsDeleted++;
        }
      }
      await fs.writeJson(this.roomsFile, validRooms);

      // 清理过期玩家
      const players = await fs.readJson(this.playersFile);
      const validPlayers = {};
      let playersDeleted = 0;
      
      for (const [playerId, player] of Object.entries(players)) {
        if (new Date(player.joined_at) > thirtyMinutesAgo && validRooms[player.room_id]) {
          validPlayers[playerId] = player;
        } else {
          playersDeleted++;
        }
      }
      await fs.writeJson(this.playersFile, validPlayers);

      // 清理孤立的答案
      const answers = await fs.readJson(this.answersFile);
      const validAnswers = {};
      let answersDeleted = 0;
      
      for (const [key, answer] of Object.entries(answers)) {
        if (validRooms[answer.room_id]) {
          validAnswers[key] = answer;
        } else {
          answersDeleted++;
        }
      }
      await fs.writeJson(this.answersFile, validAnswers);

      console.log(`清理了 ${roomsDeleted} 个过期房间, ${playersDeleted} 个过期玩家, ${answersDeleted} 个孤立答案`);
      
      return { roomsDeleted, playersDeleted, answersDeleted };
    } catch (err) {
      console.error('清理数据失败:', err.message);
      throw err;
    }
  }

  // 用户管理方法

  // 保存或更新用户信息
  async saveUser(userInfo) {
    try {
      const users = await fs.readJson(this.usersFile);
      users[userInfo.openid] = {
        ...userInfo,
        updatedAt: new Date().toISOString()
      };
      await fs.writeJson(this.usersFile, users);
      console.log('用户信息保存成功:', userInfo.openid);
      return true;
    } catch (err) {
      console.error('保存用户信息失败:', err.message);
      throw err;
    }
  }

  // 根据openid获取用户信息
  async getUserByOpenId(openid) {
    try {
      const users = await fs.readJson(this.usersFile);
      return users[openid] || null;
    } catch (err) {
      console.error('获取用户信息失败:', err.message);
      throw err;
    }
  }

  // 检查用户是否存在
  async userExists(openid) {
    try {
      const user = await this.getUserByOpenId(openid);
      return !!user;
    } catch (err) {
      console.error('检查用户存在性失败:', err.message);
      throw err;
    }
  }

  // 更新用户游戏统计
  async updateUserStats(openid, stats) {
    try {
      const users = await fs.readJson(this.usersFile);
      if (users[openid]) {
        users[openid].stats = {
          ...users[openid].stats,
          ...stats
        };
        users[openid].updatedAt = new Date().toISOString();
        await fs.writeJson(this.usersFile, users);
        return true;
      }
      return false;
    } catch (err) {
      console.error('更新用户统计失败:', err.message);
      throw err;
    }
  }

  // 关闭数据库连接（JSON文件不需要关闭）
  close() {
    console.log('JSON数据库已关闭');
  }
}

module.exports = GameDatabase;
