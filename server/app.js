const express = require('express');
const http = require('http');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const GameDatabase = require('./database');

const app = express();
const server = http.createServer(app);

// 初始化数据库
const db = new GameDatabase();

// 中间件
app.use(cors());
app.use(express.json());

// 存储在线玩家的连接
const connections = new Map(); // socketId -> {socket, playerId, roomId}
const rooms = new Map(); // roomId -> {players: [], gameData: {}}

// 生成4位房间号
function generateRoomId() {
  return Math.floor(1000 + Math.random() * 9000).toString();
}

// WebSocket处理 (使用原生WebSocket)
const WebSocket = require('ws');
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws) => {
  console.log('WebSocket连接建立');
  
  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message);
      const { type, data: payload } = data;
      
      switch (type) {
        case 'joinRoom':
          await handleJoinRoom(ws, payload);
          break;
        case 'startGame':
          await handleStartGame(ws, payload);
          break;
        case 'submitAnswer':
          await handleSubmitAnswer(ws, payload);
          break;
        default:
          console.log('未知消息类型:', type);
      }
    } catch (error) {
      console.error('处理消息失败:', error);
      sendMessage(ws, 'error', { message: '消息处理失败' });
    }
  });

  ws.on('close', () => {
    console.log('WebSocket连接关闭');
    // 清理连接信息
    for (const [socketId, info] of connections.entries()) {
      if (info.socket === ws) {
        connections.delete(socketId);
        // 从房间中移除玩家
        if (info.roomId && rooms.has(info.roomId)) {
          const room = rooms.get(info.roomId);
          room.players = room.players.filter(p => p.socketId !== socketId);
          if (room.players.length === 0) {
            rooms.delete(info.roomId);
          }
        }
        break;
      }
    }
  });
});

// 发送消息到指定WebSocket
function sendMessage(ws, type, data) {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({ type, data }));
  }
}

// 发送消息到房间内所有玩家
function sendToRoom(roomId, type, data) {
  if (rooms.has(roomId)) {
    const room = rooms.get(roomId);
    room.players.forEach(player => {
      const connection = connections.get(player.socketId);
      if (connection) {
        sendMessage(connection.socket, type, data);
      }
    });
  }
}

// 处理加入房间
async function handleJoinRoom(ws, data) {
  try {
    const { roomId, playerId, nickname } = data;
    
    // 检查房间是否存在
    const room = await db.getRoom(roomId);
    if (!room) {
      sendMessage(ws, 'error', { message: '房间不存在' });
      return;
    }

    // 初始化内存中的房间数据
    if (!rooms.has(roomId)) {
      rooms.set(roomId, { players: [], gameData: {} });
    }
    
    const roomData = rooms.get(roomId);
    
    // 检查房间是否已满
    if (roomData.players.length >= 2 && !roomData.players.find(p => p.playerId === playerId)) {
      sendMessage(ws, 'error', { message: '房间已满' });
      return;
    }

    // 生成socket ID
    const socketId = uuidv4();
    
    // 记录连接信息
    connections.set(socketId, { socket: ws, playerId, roomId });
    
    // 添加玩家到房间
    const existingPlayerIndex = roomData.players.findIndex(p => p.playerId === playerId);
    if (existingPlayerIndex >= 0) {
      // 更新现有玩家的socket
      roomData.players[existingPlayerIndex].socketId = socketId;
    } else {
      // 添加新玩家
      roomData.players.push({ playerId, nickname, socketId });
    }

    // 保存到数据库
    await db.joinRoom(playerId, roomId, nickname, socketId);

    // 通知房间内所有玩家
    sendToRoom(roomId, 'playerJoined', {
      players: roomData.players.map(p => ({ player_id: p.playerId, nickname: p.nickname })),
      playerCount: roomData.players.length
    });

    // 如果房间满员，可以开始游戏
    if (roomData.players.length === 2) {
      sendToRoom(roomId, 'roomReady', {
        message: '房间已满员，可以开始游戏'
      });
    }

    console.log(`玩家 ${nickname} 加入房间 ${roomId}`);
  } catch (error) {
    console.error('加入房间失败:', error);
    sendMessage(ws, 'error', { message: '加入房间失败' });
  }
}

// 处理开始游戏
async function handleStartGame(ws, data) {
  try {
    const { roomId } = data;
    
    // 检查房间状态
    const room = await db.getRoom(roomId);
    if (!room) {
      sendMessage(ws, 'error', { message: '房间不存在' });
      return;
    }

    const roomData = rooms.get(roomId);
    if (!roomData || roomData.players.length !== 2) {
      sendMessage(ws, 'error', { message: '需要2名玩家才能开始游戏' });
      return;
    }

    // 获取题目
    const questions = db.getQuestionsByLibrary(room.library_id, 5);
    if (questions.length < 5) {
      sendMessage(ws, 'error', { message: '题目数量不足' });
      return;
    }

    // 初始化游戏数据
    roomData.gameData = {
      questions: questions,
      currentQuestion: 0,
      score: 0,
      answers: {}
    };

    // 更新房间状态
    await db.updateRoomStatus(roomId, 'playing', 0, 0);

    // 发送第一题
    sendToRoom(roomId, 'gameStarted', {
      currentQuestion: 0,
      question: questions[0],
      totalQuestions: 5
    });

    console.log(`房间 ${roomId} 开始游戏`);
  } catch (error) {
    console.error('开始游戏失败:', error);
    sendMessage(ws, 'error', { message: '开始游戏失败' });
  }
}

// 处理提交答案
async function handleSubmitAnswer(ws, data) {
  try {
    const { roomId, playerId, questionIndex, answer } = data;
    
    const roomData = rooms.get(roomId);
    if (!roomData) {
      sendMessage(ws, 'error', { message: '房间不存在' });
      return;
    }

    // 保存答案
    await db.saveAnswer(roomId, playerId, questionIndex, answer);
    
    // 记录到内存中
    if (!roomData.gameData.answers[questionIndex]) {
      roomData.gameData.answers[questionIndex] = {};
    }
    roomData.gameData.answers[questionIndex][playerId] = answer;

    // 检查是否所有玩家都已答题
    const currentAnswers = roomData.gameData.answers[questionIndex];
    const playerCount = roomData.players.length;
    
    if (Object.keys(currentAnswers).length === playerCount) {
      // 所有玩家都已答题，判断结果
      const answers = Object.values(currentAnswers);
      const isCorrect = answers.every(a => a === answers[0]);
      
      // 更新分数
      if (isCorrect) {
        roomData.gameData.score += 1;
      }
      
      // 通知结果
      sendToRoom(roomId, 'questionResult', {
        questionIndex,
        isCorrect,
        answers: Object.entries(currentAnswers).map(([pid, ans]) => ({
          playerId: pid,
          answer: ans
        })),
        currentScore: roomData.gameData.score
      });

      // 检查是否游戏结束
      if (questionIndex >= 4) {
        // 游戏结束
        await db.updateRoomStatus(roomId, 'finished', questionIndex + 1, roomData.gameData.score);
        
        sendToRoom(roomId, 'gameFinished', {
          finalScore: roomData.gameData.score,
          totalQuestions: 5,
          percentage: Math.round((roomData.gameData.score / 5) * 100)
        });
      } else {
        // 继续下一题
        const nextQuestion = roomData.gameData.questions[questionIndex + 1];
        await db.updateRoomStatus(roomId, 'playing', questionIndex + 1, roomData.gameData.score);
        
        setTimeout(() => {
          sendToRoom(roomId, 'nextQuestion', {
            currentQuestion: questionIndex + 1,
            question: nextQuestion
          });
        }, 3000); // 3秒后显示下一题
      }
    } else {
      // 通知其他玩家有人已答题
      sendToRoom(roomId, 'playerAnswered', {
        playerId,
        answeredCount: Object.keys(currentAnswers).length,
        totalPlayers: playerCount
      });
    }
  } catch (error) {
    console.error('提交答案失败:', error);
    sendMessage(ws, 'error', { message: '提交答案失败' });
  }
}

// API路由 (保持原有的HTTP API)
app.get('/api/libraries', async (req, res) => {
  try {
    const libraries = db.getLibraries();
    res.json({
      success: true,
      data: libraries
    });
  } catch (error) {
    console.error('获取题库失败:', error);
    res.status(500).json({
      success: false,
      message: '获取题库失败'
    });
  }
});

app.post('/api/room/create', async (req, res) => {
  try {
    const { libraryId, playerNickname } = req.body;
    
    if (!libraryId || !playerNickname) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    let roomId;
    let attempts = 0;
    do {
      roomId = generateRoomId();
      const existingRoom = await db.getRoom(roomId);
      if (!existingRoom) break;
      attempts++;
    } while (attempts < 10);

    if (attempts >= 10) {
      return res.status(500).json({
        success: false,
        message: '生成房间号失败'
      });
    }

    await db.createRoom(roomId, libraryId);

    res.json({
      success: true,
      data: {
        roomId,
        libraryId
      }
    });
  } catch (error) {
    console.error('创建房间失败:', error);
    res.status(500).json({
      success: false,
      message: '创建房间失败'
    });
  }
});

app.get('/api/room/:roomId', async (req, res) => {
  try {
    const { roomId } = req.params;
    const room = await db.getRoom(roomId);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: '房间不存在'
      });
    }

    const players = await db.getRoomPlayers(roomId);

    res.json({
      success: true,
      data: {
        room,
        players,
        playerCount: players.length
      }
    });
  } catch (error) {
    console.error('获取房间信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取房间信息失败'
    });
  }
});

// 保存用户信息
app.post('/api/user/save', async (req, res) => {
  try {
    const { nickName, avatarUrl, openId } = req.body;

    if (!nickName || !openId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 这里可以保存到数据库，目前只是简单响应
    console.log('保存用户信息:', { nickName, avatarUrl, openId });

    res.json({
      success: true,
      message: '用户信息保存成功',
      data: {
        nickName,
        avatarUrl,
        openId
      }
    });
  } catch (error) {
    console.error('保存用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '保存用户信息失败'
    });
  }
});

// 定期清理过期数据
setInterval(async () => {
  try {
    await db.cleanupExpiredData();
  } catch (error) {
    console.error('清理数据失败:', error);
  }
}, 5 * 60 * 1000);

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('正在关闭服务器...');
  try {
    await db.close();
    server.close(() => {
      console.log('服务器已关闭');
      process.exit(0);
    });
  } catch (error) {
    console.error('关闭服务器失败:', error);
    process.exit(1);
  }
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`WebSocket服务已启动`);
});
