{"name": "moqi-game-server", "version": "1.0.0", "description": "默契挑战游戏后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["game", "socket.io", "sqlite", "默契挑战"], "author": "moqi-team", "license": "MIT", "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "cors": "^2.8.5", "uuid": "^9.0.0", "fs-extra": "^11.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}}