# 默契挑战游戏规划文档 (MVP版本)

## 项目概述

默契挑战是一款简单的双人在线问答游戏，两个玩家需要在同一房间内同时回答问题，只有当两人给出相同答案时才算答对。游戏旨在测试玩家之间的默契程度。

## 整体业务逻辑 (简化版)

### 核心流程
1. **题库选择** - 房主选择题库主题（查看解锁状态）
2. **房间创建** - 创建游戏房间，获得4位房间号
3. **邀请加入** - 房主分享房间号，另一位玩家加入
4. **开始游戏** - 两人都准备后自动开始
5. **同步答题** - 两人同时看到题目并提交答案
6. **结果展示** - 显示是否答案一致，继续下一题
7. **游戏结束** - 完成5道题后显示默契度，更新解锁进度

### 题库功能详细流程
1. **题库展示**
   - 显示所有题库列表
   - 已解锁题库可直接选择
   - 未解锁题库显示解锁条件

2. **解锁机制**
   - 游戏次数解锁：完成指定局数后自动解锁
   - 默契度解锁：平均默契度达到要求后解锁
   - 解锁状态存储在小程序本地存储

3. **题库选择**
   - 房主创建房间时必须选择题库
   - 加入者看到房主选择的题库信息
   - 游戏中显示当前使用的题库名称

### 简化的业务规则
- **房间容量**: 固定2人
- **题目数量**: 固定5道题
- **题目类型**: 预设选择题（4个选项）
- **题库系统**: 多个主题题库，用户可选择
- **题库解锁**: 通过游戏次数或默契度解锁新题库
- **答题时间**: 无时间限制，等待两人都提交
- **默契计算**: 答对题数 / 5 × 100%
- **房间生命周期**: 游戏结束后立即销毁
- **无断线重连**: 简化处理，断线需重新开始

## 前端技术选型

### 微信小程序 (已选择)
**优势**:
- 无需下载安装，用户获取成本低
- 微信生态内分享便利
- 跨平台兼容性好
- 开发周期短

**技术栈**:
- **框架**: 微信小程序原生框架
- **渲染引擎**: Skyline (已配置)
- **组件系统**: Glass-easel (已配置)
- **状态管理**: 小程序原生 + 自定义状态管理
- **实时通信**: WebSocket
- **UI组件库**: WeUI / Vant Weapp
- **动画**: 小程序原生动画API + Lottie
- **设计风格**: 像素卡通风格

### 前端设计风格 - 像素卡通风格

#### 视觉设计原则
- **像素艺术**: 使用8-bit/16-bit像素风格图标和插画
- **卡通色彩**: 明亮饱和的色彩搭配，营造轻松愉快氛围
- **圆角设计**: 大量使用圆角矩形，柔和友好
- **像素字体**: 使用像素风格字体增强整体一致性

#### 色彩方案
```css
/* 主色调 */
--primary-color: #FF6B6B;      /* 珊瑚红 - 主要按钮 */
--secondary-color: #4ECDC4;    /* 青绿色 - 次要元素 */
--accent-color: #FFE66D;       /* 明黄色 - 强调色 */
--success-color: #95E1D3;      /* 薄荷绿 - 成功状态 */
--warning-color: #F38BA8;      /* 粉红色 - 警告状态 */

/* 背景色 */
--bg-primary: #F7F7F7;         /* 主背景 */
--bg-secondary: #FFFFFF;       /* 卡片背景 */
--bg-dark: #2C3E50;            /* 深色背景 */

/* 文字色 */
--text-primary: #2C3E50;       /* 主要文字 */
--text-secondary: #7F8C8D;     /* 次要文字 */
--text-light: #FFFFFF;         /* 浅色文字 */
```

#### UI组件风格
- **按钮**: 像素风格边框，渐变背景，点击有像素动画效果
- **卡片**: 厚重的像素边框，投影效果模拟立体感
- **图标**: 8x8或16x16像素网格设计的图标
- **进度条**: 像素块状进度显示
- **弹窗**: 复古游戏风格的对话框设计

### 页面结构设计 (简化版)
```
pages/
├── index/           # 首页 - 创建/加入房间
├── library/         # 题库选择页面 - 选择题库主题
├── game/            # 游戏页面 - 等待+答题+结果 (合并)
└── result/          # 最终结果页面 - 默契度展示
```

## 后端技术选型 (轻量级数据库版)

### Node.js + Express + Socket.io + SQLite
**技术栈**:
- **运行时**: Node.js 18+
- **Web框架**: Express.js
- **实时通信**: Socket.io
- **数据库**: SQLite (轻量级，单文件数据库)
- **ORM**: 无ORM，直接使用 sqlite3
- **部署**: 直接运行 node app.js
- **题目数据**: 存储在数据库中

### 简化架构设计
```
server/
├── app.js              # 应用入口 + Socket逻辑
├── database.js         # 数据库连接和操作
├── questions.js        # 题目数据初始化
├── game.db             # SQLite数据库文件
└── package.json        # 依赖管理
```

**数据库表结构**:
```sql
-- 房间表
CREATE TABLE rooms (
  room_id TEXT PRIMARY KEY,
  library_id TEXT,
  status TEXT,
  current_question INTEGER,
  score INTEGER,
  created_at DATETIME
);

-- 玩家表 (临时存储在线玩家)
CREATE TABLE players (
  player_id TEXT PRIMARY KEY,
  room_id TEXT,
  nickname TEXT,
  socket_id TEXT,
  joined_at DATETIME
);

-- 答案表
CREATE TABLE answers (
  room_id TEXT,
  player_id TEXT,
  question_index INTEGER,
  answer TEXT,
  PRIMARY KEY (room_id, player_id, question_index)
);
```

## 实现思路

### 1. 数据模型设计 (简化版)

#### 数据库操作封装
```javascript
// database.js - 数据库操作封装
class GameDatabase {
  constructor() {
    this.db = new sqlite3.Database('./game.db');
    this.initTables();
  }

  // 创建房间
  async createRoom(roomId, libraryId) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO rooms (room_id, library_id, status, current_question, score, created_at)
                   VALUES (?, ?, 'waiting', 0, 0, datetime('now'))`;
      this.db.run(sql, [roomId, libraryId], function(err) {
        if (err) reject(err);
        else resolve(this.lastID);
      });
    });
  }

  // 加入房间
  async joinRoom(playerId, roomId, nickname, socketId) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT OR REPLACE INTO players (player_id, room_id, nickname, socket_id, joined_at)
                   VALUES (?, ?, ?, ?, datetime('now'))`;
      this.db.run(sql, [playerId, roomId, nickname, socketId], function(err) {
        if (err) reject(err);
        else resolve(this.changes);
      });
    });
  }

  // 获取房间信息
  async getRoom(roomId) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM rooms WHERE room_id = ?`;
      this.db.get(sql, [roomId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }
}
```

#### 题库数据结构 (硬编码)
```javascript
const questionLibraries = {
  "basic": {
    id: "basic",
    name: "基础默契",
    description: "测试基本的生活喜好",
    icon: "🎯",
    unlocked: true,  // 默认解锁
    questions: [
      {
        id: 1,
        question: "你最喜欢的颜色是？",
        options: ["红色", "蓝色", "绿色", "黄色"]
      },
      // ... 20道题
    ]
  },
  "food": {
    id: "food",
    name: "美食默契",
    description: "关于美食的喜好测试",
    icon: "🍕",
    unlocked: false,  // 需要解锁
    unlockCondition: { gamesPlayed: 3 }, // 玩3局解锁
    questions: [
      {
        id: 1,
        question: "你最喜欢的菜系是？",
        options: ["川菜", "粤菜", "湘菜", "鲁菜"]
      },
      // ... 20道题
    ]
  },
  "movie": {
    id: "movie",
    name: "影视默契",
    description: "电影电视剧喜好测试",
    icon: "🎬",
    unlocked: false,
    unlockCondition: { averageScore: 80 }, // 平均默契度80%解锁
    questions: [
      // ... 20道题
    ]
  }
};
```

### 2. 核心功能实现 (简化版)

#### 房间管理
- **创建房间**: 生成4位随机房间号，存储到内存Map
- **加入房间**: 验证房间号，检查是否已满2人
- **离开房间**: 从内存中删除玩家，房间空了就销毁

#### 实时同步
- **Socket连接**: 玩家加入时建立连接并加入房间
- **状态同步**: 房间状态变化推送给房间内所有玩家
- **答案收集**: 两人都提交答案后立即判定并进入下一题

#### 题库系统
- **多个题库**: 存储在SQLite数据库中的3-5个主题题库
- **题库选择**: 房主创建房间时选择题库
- **解锁机制**: 通过游戏次数或默契度解锁新题库
- **随机选择**: 从选定题库中随机选择5道题目
- **简单判定**: 选项完全一致才算答对

### 3. 技术实现要点 (SQLite版)

#### 实时同步
- 单服务器实例，无需考虑分布式同步
- Socket.io自动处理连接异常
- 数据持久化到SQLite，重启不丢失数据
- 简化处理：断线就重新开始游戏

#### 答案匹配
```javascript
// 简单匹配算法
function isAnswerMatch(answer1, answer2) {
  // 选择题直接比较选项索引
  return answer1 === answer2;
}
```

#### 数据库管理
```javascript
// 定期清理过期房间
setInterval(async () => {
  try {
    // 清理30分钟前创建的房间
    await db.run(`DELETE FROM rooms WHERE created_at < datetime('now', '-30 minutes')`);
    await db.run(`DELETE FROM players WHERE joined_at < datetime('now', '-30 minutes')`);
    await db.run(`DELETE FROM answers WHERE room_id NOT IN (SELECT room_id FROM rooms)`);
    console.log('清理过期数据完成');
  } catch (error) {
    console.error('清理数据失败:', error);
  }
}, 5 * 60 * 1000); // 每5分钟清理一次

// 应用启动时初始化题库数据
async function initQuestionLibraries() {
  const libraries = [
    {
      id: 'basic',
      name: '基础默契',
      description: '测试基本的生活喜好',
      icon: '🎯',
      unlocked: true
    },
    // ... 其他题库
  ];

  for (const library of libraries) {
    await db.run(`INSERT OR IGNORE INTO question_libraries VALUES (?, ?, ?, ?, ?)`,
      [library.id, library.name, library.description, library.icon, library.unlocked ? 1 : 0]);
  }
}
```

#### 题库解锁逻辑
```javascript
// 检查题库解锁状态 (前端小程序本地存储)
function checkLibraryUnlock(playerId, libraryId) {
  const library = questionLibraries[libraryId];
  if (library.unlocked) return true;

  const playerStats = wx.getStorageSync(`playerStats_${playerId}`) || {};
  const condition = library.unlockCondition;

  if (condition.gamesPlayed && playerStats.gamesPlayed >= condition.gamesPlayed) {
    return true;
  }
  if (condition.averageScore && playerStats.averageScore >= condition.averageScore) {
    return true;
  }
  return false;
}
```

### 4. 开发计划 (MVP版本)

#### 第一阶段 (4天)
- [ ] 后端基础搭建 (app.js + Socket.io)
- [ ] SQLite数据库设计和初始化
- [ ] 数据库操作封装 (database.js)
- [ ] 房间创建/加入API
- [ ] 题库数据结构设计
- [ ] 初始化3个题库到数据库，每个20道题目

#### 第二阶段 (5天)
- [ ] 前端4个页面开发 (增加题库选择页)
- [ ] 题库选择功能
- [ ] 解锁状态检查和显示
- [ ] Socket通信对接
- [ ] 游戏流程实现
- [ ] 基础UI样式

#### 第三阶段 (1天)
- [ ] 测试和调试
- [ ] 简单部署

**总开发时间: 10天 (约2周)**

### 5. MVP版本限制

#### 功能限制
- 只支持2人游戏
- 固定5道选择题
- 3个预设题库
- 解锁状态存储在小程序本地存储
- 无用户系统和登录功能
- 无分享功能
- 使用SQLite单文件数据库

#### 技术限制
- 单服务器实例
- SQLite单文件数据库，无分布式
- 无负载均衡
- 无监控和日志系统

#### 后续扩展方向
- 增加数据库持久化
- 支持多人游戏
- 添加用户系统
- 更多题库主题 (音乐、运动、旅行等)
- 自定义题库功能
- 更复杂的解锁机制 (连胜、特定组合等)
- 题库排行榜
- 添加分享功能

## 总结

**MVP版本特点**:
- **极简架构**: 单文件后端 + 3页面前端
- **快速开发**: 1周内完成基础功能
- **零成本启动**: 无数据库，直接运行
- **易于测试**: 功能简单，便于验证核心玩法

**技术栈总结**:
- 前端: 微信小程序原生 + Socket.io客户端 + 像素卡通风格UI
- 后端: Node.js + Express + Socket.io + SQLite数据库
- 部署: 单服务器直接运行，数据持久化

这个MVP版本使用轻量级SQLite数据库确保数据持久化，配合像素卡通风格的UI设计，可以快速验证产品概念。如果用户反馈良好，再逐步添加多人支持、更多题库等功能。
